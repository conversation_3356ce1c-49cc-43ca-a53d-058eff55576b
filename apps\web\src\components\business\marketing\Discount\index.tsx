'use client'

import { HomeProduct, HomeProducts } from '@ninebot/core'

import { ProductCard } from '@/components'

import { Sliders } from './slider'

type DiscountProps = {
  products: HomeProducts
  currentTime: string
}

export default function Discount({ products, currentTime }: DiscountProps) {
  return (
    <Sliders
      items={
        products?.map((product, index) => (
          <div key={product?.product?.sku} className={index === products.length - 1 ? 'm-0' : ''}>
            <ProductCard
              key={product?.product?.sku}
              product={product?.product as HomeProduct}
              currentTime={currentTime}
              isDiscountProduct={true}
            />
          </div>
        )) || []
      }
    />
  )
}
