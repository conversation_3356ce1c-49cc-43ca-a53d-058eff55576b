'use client'

import { useTranslations } from 'next-intl'
import type { ProductDetailsData, StoreListItem } from '@ninebot/core'
import { isProductConfigurable } from '@ninebot/core/src/utils/productUtils'
import { Modal } from 'antd'

import { CustomTag, InstallationService, StoreSelectorModal } from '@/businessComponents'
import { CustomImage, IconArrow, Skeleton } from '@/components'
import type {
  OptionItem,
  ProductConfigOption,
  ProductConfigurableOption,
  ProductDetails,
  ProductServiceParams,
  ProductStatus,
  SafeguardItem,
  Variant,
} from '@/types/product'

import { useCart } from '../../context/cartContext'
import { useProduct } from '../../hooks/useProduct'

import ColorSelector from './ColorSelector'
import CurrentPrice from './CurrentPrice'
import SizeSelector from './SizeSelector'

const ProductOptionsPopup = () => {
  const {
    cartState: productStatus,
    setCartState: productApi,
    handleOptionsSwitch,
    handleRefresh,
  } = useCart()
  // 从 ProductContext 获取数据和方法
  const {
    show,
    selectStore,
    productDetail,
    handlePopupClose,
    safeguardItems,
    productConfigurableOptions,
    productServiceOptions,
    handleSelectionChange,
    getStatusStock,
    isEnablebtn,
    handleAddCart,
    handleUpdateService,
    deliveryMethodPickup,
    doorVisible,
    setDoorVisible,
    setSelectStore,
    handleViewStorePop,
  } = useProduct(productStatus, productApi, handleOptionsSwitch, handleRefresh) as unknown as {
    show: boolean
    selectStore: StoreListItem | null
    productStatus: ProductStatus
    productDetail: ProductDetails & { variants?: Variant[] }
    handlePopupClose: () => void
    safeguardItems: SafeguardItem[]
    productConfigurableOptions: ProductConfigurableOption[]
    productServiceOptions: ProductConfigOption[]
    handleSelectionChange: (item: OptionItem, optionId: string) => void
    getStatusStock: () => boolean
    isEnablebtn: () => boolean
    handleAddCart: () => void
    handleUpdateService: (params: ProductServiceParams) => void
    deliveryMethodPickup: boolean
    doorVisible: boolean
    setDoorVisible: () => void
    setSelectStore: (store: StoreListItem | null) => void
    handleViewStorePop: () => void
  }
  const getI18nString = useTranslations('Common')

  const price = productStatus.price
  const paymentNcoin =
    productStatus?.productItem?.paymeng_method?.includes('payment_method_ncoin') || false
  const servicePrice = productStatus.servicePrice || 0
  const storePrice = selectStore ? (selectStore as StoreListItem).price : 0
  const isPromote = price ? price.final_price.value !== price.regular_price.value : false

  const onConfirmCallback = (store: StoreListItem | null) => {
    setSelectStore(store)
    productApi({
      ...productStatus,
      popVisible: true,
    })
  }

  return (
    <>
      <Modal
        className="custom_modal"
        closable={false}
        open={productStatus.popVisible}
        onCancel={handlePopupClose}
        onOk={getStatusStock() ? handleAddCart : handlePopupClose}
        centered
        maskClosable={false}
        okButtonProps={{ disabled: isEnablebtn() }}
        closeIcon={null}
        onClose={handlePopupClose}
        okText={getStatusStock() ? getI18nString('confirm') : getI18nString('product_sale_out')}
        cancelText="取消"
        width={620}>
        {show ? (
          <div className="flex gap-base-12">
            {productStatus.image && productStatus.image.url ? (
              <CustomImage
                src={productStatus.image.url}
                alt="商品图片"
                width={120}
                height={120}
                displayMode="fixed"
                borderRadius={8}
                objectFit="cover"
              />
            ) : (
              <div className="h-[120px] w-[120px] rounded-lg bg-gray-100"></div>
            )}

            <div className="flex-1">
              <div className="mb-base-24 flex flex-col gap-base-12">
                {productStatus.selectOptionName
                  ? `${getI18nString('product_selected')}：${productStatus.selectOptionName}`
                  : ''}
                {price && (
                  <CurrentPrice
                    price={price}
                    paymentNcoin={paymentNcoin}
                    servicePrice={servicePrice}
                    storePrice={storePrice}
                    isPromote={isPromote}
                  />
                )}
                {safeguardItems.length > 0 && (
                  <div className="flex items-center gap-2">
                    {safeguardItems.map(
                      (clause) =>
                        clause.value && <CustomTag key={clause.value} text={clause.label} />,
                    )}
                  </div>
                )}
              </div>

              {/* 商品选项 */}
              <div className="flex flex-col gap-12 border-y border-y-[#E6E6E6] py-base-24">
                <div className="flex max-h-[260px] flex-col gap-base-32 overflow-y-auto">
                  {isProductConfigurable(productDetail as unknown as ProductDetailsData) &&
                    productConfigurableOptions?.map((option) => (
                      <div key={option.attribute_id}>
                        <div className="mb-base-12 text-base">{option.label}</div>
                        {option.attribute_code.includes('color') ? (
                          <ColorSelector
                            optionItems={option.values}
                            id={option.attribute_id}
                            variants={productDetail?.variants ?? []}
                            productStatus={productStatus}
                            onSelectionChange={handleSelectionChange}
                          />
                        ) : (
                          <SizeSelector
                            optionItems={option.values}
                            id={option.attribute_id}
                            productStatus={productStatus}
                            onSelectionChange={handleSelectionChange}
                          />
                        )}
                      </div>
                    ))}
                </div>

                {/* 安装服务 */}
                {productServiceOptions.length > 0 && (
                  <InstallationService
                    productConfigOptions={productServiceOptions}
                    productStatus={productStatus}
                    handleUpdateService={handleUpdateService}
                  />
                )}

                {/* 门店选择 */}
                {deliveryMethodPickup && (
                  <div>
                    <div className="mb-base-12 flex items-center justify-between text-lg">
                      <span>{getI18nString('store address')}：</span>
                      <button className="flex items-center gap-[4px]" onClick={handleViewStorePop}>
                        <span className="text-[14px] leading-[16px]">
                          {getI18nString('change_address')}
                        </span>
                        <IconArrow rotate={-90} />
                      </button>
                    </div>
                    <div className="mb-base-12 font-miSansRegular330 text-base text-[#444446]">
                      {selectStore?.store_address}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex gap-base-12">
            {/* 商品图片骨架 */}
            <Skeleton shape="square" width={120} height={120} borderRadius={8} animated={true} />

            <div className="flex-1">
              {/* 选项名称和价格骨架 */}
              <div className="mb-base-24 flex flex-col gap-base-12">
                <Skeleton width="70%" height={24} animated={true} />
                <Skeleton width="40%" height={32} animated={true} />
                <div className="flex items-center gap-2">
                  <Skeleton width={60} height={24} shape="round" animated={true} />
                  <Skeleton width={80} height={24} shape="round" animated={true} />
                </div>
              </div>

              {/* 商品选项骨架 */}
              <div className="flex flex-col gap-12 border-y border-y-[#E6E6E6] py-base-24">
                <div className="flex max-h-[260px] flex-col gap-base-32 overflow-y-auto">
                  {/* 颜色选项骨架 */}
                  <div>
                    <Skeleton
                      width={100}
                      height={24}
                      animated={true}
                      style={{ marginBottom: '12px' }}
                    />
                    <div className="flex gap-3">
                      {[1, 2, 3].map((i) => (
                        <Skeleton key={i} shape="circle" width={40} height={40} animated={true} />
                      ))}
                    </div>
                  </div>

                  {/* 尺寸选项骨架 */}
                  <div>
                    <Skeleton
                      width={100}
                      height={24}
                      animated={true}
                      style={{ marginBottom: '12px' }}
                    />
                    <div className="flex gap-3">
                      {[1, 2, 3].map((i) => (
                        <Skeleton key={i} shape="round" width={60} height={36} animated={true} />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {deliveryMethodPickup && (
        <StoreSelectorModal
          doorVisible={doorVisible}
          setDoorVisible={setDoorVisible}
          selectStore={selectStore}
          onConfirmCallback={onConfirmCallback}
          productId={productStatus.id}
        />
      )}
    </>
  )
}

export default ProductOptionsPopup
