'use client'
import { forwardRef } from 'react'
import Image from 'next/image'
import { ROUTE, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import loadingIcon from '@ninebot/core/src/assets/images/loading-dark.webp'
import { useNavigate } from '@ninebot/core/src/businessHooks'

import { Category } from '@/types/category'

interface NavigationDropdownProps {
  isOpen: boolean
  categories: Category
  isLoading?: boolean
  onClose: () => void
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

const NavigationDropdown = forwardRef<HTMLDivElement, NavigationDropdownProps>(
  ({ isOpen, categories, isLoading = false, onClose, onMouseEnter, onMouseLeave }, ref) => {
    const { reportEvent } = useVolcAnalytics()
    const { openPage } = useNavigate()

    if (!isOpen) return null

    // 加载状态
    if (isLoading) {
      return (
        <div
          ref={ref}
          className="absolute left-0 right-0 top-full z-50 bg-white pb-16 pt-8"
          onMouseEnter={onMouseEnter}
          onMouseLeave={() => {
            onMouseLeave?.()
            onClose()
          }}>
          <div className="max-container flex items-center justify-center gap-[24px]">
            <div className="py-8 text-center">
              <Image
                priority
                src={loadingIcon}
                loading="eager"
                width={60}
                height={4}
                alt="Loading..."
              />
              {/* <p className="mt-2 text-gray-500">加载中...</p> */}
            </div>
          </div>
        </div>
      )
    }

    // 无数据状态
    if (!categories.length) {
      return (
        <div
          ref={ref}
          className="absolute left-0 right-0 top-full z-50 bg-white pb-16 pt-8 shadow-2xl"
          style={{
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          }}
          onMouseEnter={onMouseEnter}
          onMouseLeave={() => {
            onMouseLeave?.()
            onClose()
          }}>
          <div className="max-container flex items-center justify-center gap-[24px]">
            <p className="text-gray-500">暂无分类数据</p>
          </div>
        </div>
      )
    }

    return (
      <div
        ref={ref}
        data-dropdown
        data-navigation-dropdown
        className="absolute left-0 right-0 top-full z-50 bg-white pb-[64px] pt-[32px]"
        onMouseEnter={onMouseEnter}
        onMouseLeave={() => {
          onMouseLeave?.()
          onClose()
        }}>
        <div className="max-container-no-mb flex flex-wrap gap-x-48 gap-y-[24px]">
          {categories.map((section, sectionIndex) => (
            <div key={sectionIndex} className="flex flex-col gap-[12px]">
              <div
                className="cursor-pointer font-miSansDemiBold450 text-[14px] capitalize leading-[140%] text-[#86868B] hover:text-primary"
                onClick={() => {
                  // 埋点：点击分类
                  reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
                    category_id: section?.uid,
                    category_name: section?.name,
                  })
                  onClose()
                  if (section) {
                    openPage({
                      route: ROUTE.catalogCategory,
                      url: `/${section.url_path}${section.url_suffix}`,
                      value: section.uid,
                    })
                  }
                }}>
                {section?.name}
              </div>
              <div className="flex flex-col gap-[12px]">
                {section?.children &&
                  section.children.map((item) => {
                    if (item?.include_in_menu) {
                      return (
                        <div
                          key={item.uid}
                          className="cursor-pointer font-miSansMedium380 text-[18px] leading-[24px] text-[#222223] hover:text-primary"
                          onClick={() => {
                            // 埋点：点击分类
                            reportEvent(TRACK_EVENT.shop_homepage_tab_click, {
                              category_id: item.uid,
                              category_name: item?.name,
                            })
                            onClose()
                            openPage({
                              route: ROUTE.catalogCategory,
                              url: `/${item.url_path}${item.url_suffix}`,
                              value: item.uid,
                            })
                          }}>
                          {item.name}
                        </div>
                      )
                    }

                    return null
                  })}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  },
)

NavigationDropdown.displayName = 'NavigationDropdown'

export default NavigationDropdown
