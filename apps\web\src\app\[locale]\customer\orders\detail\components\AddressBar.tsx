import { Address2, House, Phone2 } from './icons'

export default function AddressBar({
  name,
  address,
  phone,
}: {
  name?: string
  address?: string
  phone?: string
}) {
  return (
    <div
      className={`flex items-center rounded-base bg-[#F8F8F9] px-base-12 py-base font-miSansRegular330 text-[14px] leading-none text-[#444446] ${name ? 'justify-between' : 'gap-base-16'}`}>
      <div className="flex items-center justify-between gap-base-32">
        {name ? (
          <div className="flex items-center gap-[4px]">
            <House />
            {name}
          </div>
        ) : null}
        <div className="flex items-center gap-[4px]">
          <Address2 />
          {address}
        </div>
      </div>
      <div className="flex items-center gap-[4px]">
        <Phone2 />
        {phone}
      </div>
    </div>
  )
}
