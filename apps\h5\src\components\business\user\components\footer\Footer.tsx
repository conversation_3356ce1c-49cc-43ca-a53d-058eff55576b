'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  decodeBase64,
  IconPlus,
  mergeStyles,
  NCoinView,
  OrderDetail,
  PaymentMethod,
  Price,
  resolveCatchMessage,
  setCancelOrderId,
  sleep,
  useCancelOrderMutation,
  useCancelOrderReturnMutation,
  useDebounceFn,
  useTimerCoundown,
  useUserOrder,
} from '@ninebot/core'
import { TToastProps } from '@ninebot/core/src/components/toast/Toast'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Button, Dialog } from 'antd-mobile'

import { commonStyles } from '@/constants'

type FooterProps = {
  order: OrderDetail
  hasNCoin: boolean
  isOnlyNCoin: boolean
  toast: {
    show: (params: TToastProps) => void
    hide: () => void
  }
  loading: {
    show: () => void
    hide: () => void
  }
  currentTime: number
  setPaymentVisible: React.Dispatch<React.SetStateAction<boolean>>
  setPaymentsList: React.Dispatch<React.SetStateAction<PaymentMethod[]>>
  refetchOrderDetail: () => void
  handleReturnOrder: () => void
}

/**
 * 订单详情 Footer
 */
const Footer = (props: FooterProps) => {
  const {
    order,
    hasNCoin,
    isOnlyNCoin,
    toast,
    loading,
    currentTime,
    setPaymentVisible,
    setPaymentsList,
    refetchOrderDetail,
    handleReturnOrder,
  } = props

  const [now, setNow] = useState(currentTime)
  const getI18nString = useTranslations('Common')
  const dispatch = useAppDispatch()

  const { checkOrderPayInfo, getAvailablePayments } = useUserOrder()

  const onEnd = useCallback(() => {
    setNow(new Date().getTime())
    setTimeout(() => {
      refetchOrderDetail()
    }, 200)
  }, [refetchOrderDetail])

  /**
   * 监听当回到当前页面时，重置倒计时
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setNow(new Date().getTime())
      }
    }

    const handleFocus = () => {
      setNow(new Date().getTime())
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [])

  const formattedRes = useTimerCoundown(
    now / 1000,
    order?.status_code === 'pending' ? Number(order?.payment_time_out) : 0,
    1000,
    onEnd,
  )

  const [cancelOrder] = useCancelOrderMutation()
  const [cancelOrderReturn] = useCancelOrderReturnMutation()
  /**
   * 是否待付款
   */
  const isWaitPay = useMemo(
    () => order?.status_code === 'pending' && Number(order?.payment_time_out) > now / 1000,
    [order?.status_code, order?.payment_time_out, now],
  )

  /**
   * 是否已发货
   */
  const isDelivered = useMemo(
    () => order?.status_code === 'shipped' || order?.status_code === 'delivered',
    [order?.status_code],
  )

  /**
   * 是否可取消申请
   */
  const isCanCancel = useMemo(
    () => order?.can_cancel_requisition === true,
    [order?.can_cancel_requisition],
  )

  /**
   * 是否可退货退款
   */
  const isReturnAble = useMemo(() => order?.can_requisition === true, [order?.can_requisition])

  /**
   * 跳转支付
   */
  const { run: handleRepay } = useDebounceFn(async () => {
    if (order?.encrypt?.nid && order?.payment_methods?.[0]?.type && order?.total?.grand_total) {
      const result = await checkOrderPayInfo(order.encrypt.nid)
      // 202 跳转支付
      if (result?.code === 202) {
        const payments = await getAvailablePayments(Number(order?.total?.grand_total?.value))
        if (payments?.length) {
          setPaymentVisible(true)
          setPaymentsList(payments as PaymentMethod[])
          return
        }
        return
      }
      // 200 已支付
      if (result?.code === 200) {
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: getI18nString('order_has_paid'),
        })
        await sleep(500)
        refetchOrderDetail()
        return
      }
      // 支付失败
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: result?.message || getI18nString('fetch_data_error'),
      })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 取消订单
   */
  const { run: confirmCancelOrder } = useDebounceFn(() => {
    if (order?.id) {
      loading.show()
      cancelOrder({
        input: {
          reason: getI18nString('proactively_cancel_the_order'),
          order_id: decodeBase64(order.id),
        },
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.cancelOrder?.order?.id) {
            toast.show({
              icon: 'success',
              content: getI18nString('order_has_canceled'),
            })
            await sleep(500)
            // 更新订单详情页
            refetchOrderDetail()
            // 更新订单列表页
            dispatch(setCancelOrderId(order.number))
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 取消订单确认弹窗
   */
  const handleCancelOrder = () => {
    Dialog.confirm({
      bodyClassName: 'custom-dialog-confirm',
      title: getI18nString('confirm_cancel_order'),
      cancelText: getI18nString('thinking'),
      confirmText: getI18nString('cancel_order'),
      onConfirm: () => {
        confirmCancelOrder()
      },
    })
  }

  /**
   * 取消退款申请
   */
  const { run: handleCancelReturnOrder } = useDebounceFn(() => {
    if (order?.encrypt?.nid) {
      loading.show()
      cancelOrderReturn({
        orderNumber: order.encrypt.nid,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.cancelOrderRequisition?.status) {
            toast.show({
              icon: 'success',
              content: getI18nString('cancelled'),
            })
            await sleep(500)
            refetchOrderDetail()
          } else {
            toast.show({
              icon: 'fail',
              content: res?.cancelOrderRequisition?.message || getI18nString('fetch_data_error'),
            })
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 转化时间
   */
  const formatTime = useCallback((time: { minutes: number; seconds: number }) => {
    const { minutes, seconds } = time

    const pad = (num: number) => String(num).padStart(2, '0')
    return [pad(minutes), pad(seconds)]
  }, [])

  const [minutes, seconds] = formatTime(formattedRes)

  return isWaitPay || isReturnAble || isCanCancel ? (
    <>
      <div className="h-[72px]" />
      <div
        className={`max-container fixed bottom-0 z-50 flex h-[72px] items-center bg-white p-[12px] shadow-[0_-2px_8px_rgba(0,0,0,0.06)] ${
          !isWaitPay ? 'justify-end' : 'justify-between'
        }`}>
        {isWaitPay && (
          <>
            <button
              style={{
                ...(hasNCoin && !isOnlyNCoin
                  ? { marginRight: 4, padding: '6px 8px' }
                  : { marginRight: 8, padding: '8px 14px' }),
                borderWidth: 1,
                borderColor: '#E1E1E4',
                borderRadius: 999,
                whiteSpace: 'nowrap',
              }}
              onClick={handleCancelOrder}>
              <div className={commonStyles.font_14}>{getI18nString('cancel_order')}</div>
            </button>
            <div className="flex items-center gap-[12px]">
              <div className="flex items-baseline">
                {isOnlyNCoin ? (
                  <>
                    <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#86868B]">
                      {getI18nString('amount_to')}
                    </div>
                    <NCoinView
                      number={order?.ncoin_pay?.grand_total}
                      iconStyle={{ size: 14 }}
                      textStyle={mergeStyles([commonStyles.font_14, 'leading-[18px]'])}
                    />
                  </>
                ) : hasNCoin ? (
                  <div>
                    <div className="flex items-baseline">
                      <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#86868B]">
                        {getI18nString('amount_to')}
                      </div>
                      <Price
                        price={order?.total?.grand_total}
                        currencyStyle={mergeStyles([
                          commonStyles.font_14_bold,
                          commonStyles.font_red,
                        ])}
                        textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                        fractionStyle={mergeStyles([
                          commonStyles.font_14_bold,
                          commonStyles.font_red,
                        ])}
                      />
                    </div>
                    <div className="flex items-baseline">
                      <div className="ml-[18px] mr-4">
                        <IconPlus />
                      </div>
                      <NCoinView
                        number={order?.ncoin_pay?.grand_total}
                        iconStyle={{ size: 14 }}
                        textStyle={mergeStyles([commonStyles.font_14, 'leading-[18px]'])}
                      />
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#86868B]">
                      {getI18nString('amount_to')}
                    </div>
                    <Price
                      price={order?.total?.grand_total}
                      currencyStyle={mergeStyles([
                        commonStyles.font_14_bold,
                        commonStyles.font_red,
                      ])}
                      textStyle={mergeStyles([commonStyles.font_18_bold, commonStyles.font_red])}
                      fractionStyle={mergeStyles([
                        commonStyles.font_14_bold,
                        commonStyles.font_red,
                      ])}
                    />
                  </>
                )}
              </div>
              <Button
                className="nb-button w-[116px]"
                style={{
                  padding: '0 1.6rem',
                  height: '4.4rem',
                }}
                color="primary"
                onClick={handleRepay}>
                <div className="flex flex-col items-center justify-center">
                  <div
                    className={
                      hasNCoin && !isOnlyNCoin
                        ? mergeStyles([commonStyles.font_14_bold, commonStyles.font_white])
                        : mergeStyles([
                            commonStyles.font_16_bold,
                            commonStyles.font_white,
                            'text-center',
                          ])
                    }>
                    {getI18nString('continue_pay')}
                  </div>
                  <div
                    className={`flex flex-row text-center font-miSansRegular330 text-[12px] leading-[16px] text-white`}>
                    <div>{getI18nString('left')} </div>
                    <div>
                      {minutes}:{seconds}
                    </div>
                  </div>
                </div>
              </Button>
            </div>
          </>
        )}

        {isCanCancel ? (
          <Button className="nb-button w-[144px]" onClick={handleCancelReturnOrder}>
            <div className={`${commonStyles.font_16_bold} mt-[2px]`}>
              {getI18nString('cancel_return')}
            </div>
          </Button>
        ) : (
          isReturnAble && (
            <Button className="nb-button !h-[44px] w-[144px]" onClick={handleReturnOrder}>
              <div className={`${commonStyles.font_16_bold} mt-0 !leading-none`}>
                {isDelivered ? getI18nString('return_and_refund') : getI18nString('refund')}
              </div>
            </Button>
          )
        )}
      </div>
    </>
  ) : null
}

export default Footer
