'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  type Address,
  cn,
  LocalToastProvider,
  mergeStyles,
  resolveCatchMessage,
  TCatchMessage,
  useDebounceFn,
  useLazyGetProvinceCityCountyQuery,
  useLocalToastContext,
} from '@ninebot/core'
import { Button, Checkbox, Form, Input, Space } from 'antd'

import { CustomDrawer, IconArrow } from '@/components'

interface AddressEditDrawerProps {
  visible: boolean
  onClose: () => void
  editingAddress?: Address | null
  onSubmit: (values: Address) => void
  onGoBack?: () => void
}

interface FormValues {
  receive_name: string
  receive_phone: string
  province: string
  city: string
  county: string
  street: string
  is_default: boolean
  tag: string
}

interface ApiAreaItem {
  default_name: string
  region_id?: string
  city_id?: string
  district_id?: string
}

interface AreaItem {
  label: string
  id: string
}

// const ADDRESS_TAGS = [
//   { label: '学校', value: 'school' },
//   { label: '家', value: 'home' },
//   { label: '公司', value: 'company' },
// ]

const TipIcon = () => {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 1.5C3.23857 1.5 0.999999 3.73858 1 6.5C1 9.26143 3.23857 11.5 6 11.5C8.76143 11.5 11 9.26143 11 6.5C11 3.73857 8.76142 1.5 6 1.5Z"
        stroke="#DA291C"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 9.05078L6.5 8.55078L6 8.05078L5.5 8.55078L5.5 9.05078L6.5 9.05078Z"
        fill="#DA291C"
      />
      <path
        d="M5.5 3.98118L5.5 7.07031L6 7.57031L6.5 7.07031L6.5 3.98118L5.5 3.98118Z"
        fill="#DA291C"
      />
    </svg>
  )
}

// 内部组件，使用 LocalToastProvider 的上下文
function AddressEditContent({
  visible,
  onClose,
  editingAddress,
  onSubmit,
  onGoBack,
}: AddressEditDrawerProps) {
  const getI18nString = useTranslations('Common')
  const [form] = Form.useForm<FormValues>()

  const [selectedTag, setSelectedTag] = useState<string>('')
  const [customTag, setCustomTag] = useState<string>('')
  const [isCustomTag, setIsCustomTag] = useState(false)

  // 地区选择相关状态
  const toast = useLocalToastContext()
  const containerRef = useRef<HTMLDivElement>(null)
  const [getAddressData] = useLazyGetProvinceCityCountyQuery()

  const [activeTab, setActiveTab] = useState<'province' | 'city' | 'district'>('province')
  const [activeProvince, setActiveProvince] = useState<AreaItem>({ label: '省份', id: '' })
  const [activeCity, setActiveCity] = useState<AreaItem>({ label: '城市', id: '' })
  const [activeDistrict, setActiveDistrict] = useState<AreaItem>({ label: '县区', id: '' })

  const [provinces, setProvinces] = useState<AreaItem[]>([])
  const [cities, setCities] = useState<AreaItem[]>([])
  const [districts, setDistricts] = useState<AreaItem[]>([])

  const [addressSelect, setAddressSelect] = useState(false)

  // 地址选择器验证状态
  const [addressValidateStatus, setAddressValidateStatus] = useState<'error' | ''>('')

  /**
   * 获取地址数据
   */
  const fetchAddressData = useCallback(
    ({ id, type }: { id?: string; type: 'region' | 'city' }) => {
      getAddressData({ FilterValue: id, type })
        .unwrap()
        .then((res) => {
          if (!res?.province_city_county_search) return
          const parsedData = JSON.parse(res.province_city_county_search) as ApiAreaItem[]
          if (id && type === 'city') {
            const formatData = parsedData.map((item) => ({
              id: item.district_id || '',
              label: item.default_name,
            }))
            setDistricts(formatData)
          } else if (id && type === 'region') {
            const formatData = parsedData.map((item) => ({
              id: item.city_id || '',
              label: item.default_name,
            }))
            setCities(formatData)
          } else {
            const formatData = parsedData.map((item) => ({
              id: item.region_id || '',
              label: item.default_name,
            }))
            setProvinces(formatData)
          }
        })
        .catch((error) => {
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error as TCatchMessage) as string,
          })
        })
    },
    [getAddressData, toast],
  )

  /**
   * 获取省份
   */
  useEffect(() => {
    fetchAddressData({ type: 'region' })
  }, [fetchAddressData])

  /**
   * 获取城市
   */
  useEffect(() => {
    if (activeProvince.id) {
      fetchAddressData({ id: activeProvince.id, type: 'region' })
    } else {
      setCities([])
    }
  }, [fetchAddressData, activeProvince.id])

  /**
   * 获取区
   */
  useEffect(() => {
    if (activeCity.id) {
      fetchAddressData({ id: activeCity.id, type: 'city' })
    } else {
      setDistricts([])
    }
  }, [fetchAddressData, activeCity.id])

  /**
   * 地址列表
   */
  const addressList = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return provinces
      case 'city':
        return cities
      case 'district':
        return districts
      default:
        return []
    }
  }, [activeTab, provinces, cities, districts])

  /**
   * 当前选中的item
   */
  const currentAddress = useMemo(() => {
    switch (activeTab) {
      case 'province':
        return activeProvince
      case 'city':
        return activeCity
      case 'district':
        return activeDistrict
      default:
        return { label: '省份', id: '' }
    }
  }, [activeTab, activeProvince, activeCity, activeDistrict])

  /**
   * 选择地址
   */
  const handleAddressSelect = (address: AreaItem) => {
    // 清除验证错误状态
    setAddressValidateStatus('')
    // 清除表单字段的验证错误信息
    form.setFields([
      {
        name: 'province',
        errors: [],
      },
    ])
    switch (activeTab) {
      case 'province':
        setActiveProvince(address)
        setActiveCity({ label: '城市', id: '' })
        setActiveDistrict({ label: '县区', id: '' })
        setActiveTab('city')
        break
      case 'city':
        setActiveCity(address)
        setActiveDistrict({ label: '县区', id: '' })
        setActiveTab('district')
        break
      case 'district':
        setActiveDistrict(address)
        setAddressSelect(false)
        break
    }
  }

  /**
   * 打开地址下拉
   */
  const handleAddressDropdown = (tab: 'province' | 'city' | 'district') => {
    if (tab === 'city' && activeProvince.id === '') {
      return
    }
    if (tab === 'district' && (activeProvince.id === '' || activeCity.id === '')) {
      return
    }
    setActiveTab(tab)
    setAddressSelect(true)
  }

  /**
   * 设置默认省份
   */
  useEffect(() => {
    if (provinces.length > 0 && editingAddress?.province) {
      const defaultProvince = provinces.find(
        (province) => province.label === editingAddress.province,
      )
      if (defaultProvince) {
        setActiveProvince(defaultProvince)
      }
    }
  }, [provinces, editingAddress?.province])

  /**
   * 设置默认城市
   */
  useEffect(() => {
    if (cities.length > 0 && editingAddress?.city) {
      const defaultCity = cities.find((city) => city.label === editingAddress.city)
      if (defaultCity) {
        setActiveCity(defaultCity)
      }
    }
  }, [cities, editingAddress?.city])

  /**
   * 设置默认区
   */
  useEffect(() => {
    if (districts.length > 0 && editingAddress?.county) {
      const defaultDistrict = districts.find((district) => district.label === editingAddress.county)
      if (defaultDistrict) {
        setActiveDistrict(defaultDistrict)
      }
    }
  }, [districts, editingAddress?.county])

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (visible && editingAddress) {
      form.setFieldsValue({
        receive_name: editingAddress.receive_name || '',
        receive_phone: editingAddress.receive_phone || '',
        province: editingAddress.province || '',
        city: editingAddress.city || '',
        county: editingAddress.county || '',
        street: editingAddress.street || '',
        is_default: editingAddress.is_default || false,
      })

      // 设置标签
      // const existingTag = ADDRESS_TAGS.find((tag) => tag.value === editingAddress.tag)
      // if (existingTag) {
      //   setSelectedTag(existingTag.value)
      //   setIsCustomTag(false)
      // } else if (editingAddress.tag) {
      //   setCustomTag(editingAddress.tag)
      //   setIsCustomTag(true)
      // }
    } else if (visible && !editingAddress) {
      // 新增地址时重置表单
      form.resetFields()
      setSelectedTag('')
      setCustomTag('')
      setIsCustomTag(false)
      // 重置地区选择
      setActiveProvince({ label: '省份', id: '' })
      setActiveCity({ label: '城市', id: '' })
      setActiveDistrict({ label: '县区', id: '' })
      setAddressSelect(false)
      setAddressValidateStatus('')
    }
  }, [visible, editingAddress, form])

  /**
   * 处理标签选择
   */
  // const handleTagSelect = (tag: string) => {
  //   setSelectedTag(tag)
  //   setIsCustomTag(false)
  //   setCustomTag('')
  // }

  /**
   * 处理自定义标签
   */
  // const handleCustomTag = () => {
  //   setIsCustomTag(true)
  //   setSelectedTag('')
  // }

  /**
   * 提交表单
   */
  const { run: handleSubmit } = useDebounceFn(async () => {
    try {
      const values = await form.validateFields()

      // 确定最终的标签值
      let finalTag = ''
      if (isCustomTag && customTag.trim()) {
        finalTag = customTag.trim()
      } else if (selectedTag) {
        finalTag = selectedTag
      }

      const addressData: Address = {
        ...editingAddress,
        ...values,
        tag: finalTag,
        province: activeProvince.label,
        city: activeCity.label,
        county: activeDistrict.label,
        // 确保必要的字段存在
        address_id: editingAddress?.address_id,
      }

      onSubmit(addressData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  })

  /**
   * 关闭弹窗并返回上一步
   */
  const handleClose = () => {
    form.resetFields()
    setSelectedTag('')
    setCustomTag('')
    setIsCustomTag(false)
    setAddressValidateStatus('')
    // 先关闭当前抽屉
    onClose()
    // 添加延迟，确保当前抽屉完全关闭后再打开上一个抽屉
    setTimeout(() => {
      // 这里需要传入一个回调函数来重新打开地址管理抽屉
      if (onGoBack) {
        onGoBack()
      }
    }, 300)
  }

  return (
    <>
      <CustomDrawer
        title={editingAddress ? '寄件地址编辑' : '添加寄件地址'}
        onClose={handleClose}
        open={visible}
        footer={
          <div className="flex items-center gap-base-16">
            <Button style={{ width: '144px' }} onClick={handleClose}>
              {getI18nString('previous')}
            </Button>
            <Button type="primary" style={{ flex: 1 }} onClick={handleSubmit}>
              {getI18nString('confirm')}
            </Button>
          </div>
        }>
        <LocalToastProvider
          containerRef={containerRef}
          position="center"
          contentClassName="gap-[8px] px-[16px] py-[12px]"
          textClassName="text-[16px] leading-[1.4]"
          iconWidth={22}
          iconHeight={22}>
          <div ref={containerRef} className="relative pt-base-24">
            <Form
              form={form}
              layout="vertical"
              className="address-form space-y-[24px]"
              requiredMark={false}>
              {/* 收件人 */}
              <Form.Item
                className="table-item overflow-hidden"
                name="receive_name"
                label={
                  <span className="font-miSansDemiBold450 text-[16px] leading-[120%] text-[#0F0F0F]">
                    收件人 <span className="text-[#DA291C]">*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: (
                      <div className="flex items-center gap-[4px]">
                        <TipIcon />
                        请填写收货人姓名
                      </div>
                    ),
                  },
                ]}>
                <Input
                  placeholder="请填写收货人姓名"
                  className="h-[48px] rounded-[8px] border-[#D1D1D4]"
                />
              </Form.Item>

              {/* 手机号 */}
              <div className="flex flex-col gap-[12px]">
                <span className="font-miSansDemiBold450 text-[16px] leading-[120%] text-[#0F0F0F]">
                  手机号 <span className="text-[#DA291C]">*</span>
                </span>
                <Space.Compact block className="gap-base-12">
                  <Form.Item
                    style={{ flex: 1, marginBottom: 0 }}
                    name="receive_phone"
                    label=""
                    rules={[
                      {
                        required: true,
                        message: (
                          <div className="flex items-center gap-[4px]">
                            <TipIcon />
                            请填写收货人手机号
                          </div>
                        ),
                      },
                      {
                        pattern: /^1[3-9]\d{9}$/,
                        message: (
                          <div className="flex items-center gap-[4px]">
                            <TipIcon />
                            请输入正确的手机号
                          </div>
                        ),
                      },
                    ]}>
                    <Input
                      maxLength={11}
                      type="tel"
                      inputMode="tel"
                      style={{ height: '48px', padding: '12px', borderRadius: '8px' }}
                      placeholder="请填写收货人手机号"
                      onInput={(e) => {
                        // 只允许输入数字，并限制长度为11位
                        const target = e.target as HTMLInputElement
                        target.value = target.value.replace(/[^\d]/g, '').slice(0, 11)
                      }}
                    />
                  </Form.Item>
                </Space.Compact>
              </div>

              {/* 所在地区 */}
              <Form.Item
                name="province"
                label={
                  <span className="font-miSansDemiBold450 text-[16px] leading-[120%] text-[#0F0F0F]">
                    所在地区 <span className="text-[#DA291C]">*</span>
                  </span>
                }
                // className="address-select"
                rules={[
                  {
                    required: true,
                    validator: async () => {
                      if (!activeProvince.id || !activeCity.id || !activeDistrict.id) {
                        setAddressValidateStatus('error')
                        return Promise.reject({
                          message: (
                            <div className="flex items-center gap-[4px]">
                              <TipIcon />
                              请选择完整的地区信息
                            </div>
                          ),
                        })
                      }
                      setAddressValidateStatus('')
                      // 清除表单字段的验证错误信息
                      form.setFields([
                        {
                          name: 'province',
                          errors: [],
                        },
                      ])
                      return Promise.resolve()
                    },
                  },
                ]}>
                <div className="relative flex h-[48px] w-full flex-col">
                  <div
                    className={mergeStyles([
                      'flex h-[48px] w-full cursor-pointer items-center justify-between rounded-[8px] border px-[12px]',
                      // 根据验证状态设置边框颜色
                      addressValidateStatus === 'error' ? 'border-[#DA291C]' : 'border-[#d9d9d9]',
                    ])}
                    onClick={() => {
                      setActiveTab('province')
                      setAddressSelect((pre) => !pre)
                    }}>
                    {activeProvince.label && (
                      <div className="flex h-[48px] flex-row items-center text-[#00000066]">
                        <div
                          onClick={(e) => {
                            e.stopPropagation()
                            handleAddressDropdown('province')
                          }}
                          className={cn([
                            'font-miSansRegular330',
                            addressSelect && currentAddress.label === activeProvince.label
                              ? 'text-[#DA291C]'
                              : 'text-[#000000]',
                            'mr-[4px] cursor-pointer',
                          ])}>
                          {activeProvince.label}
                        </div>
                        <div
                          className={mergeStyles(
                            activeProvince.label !== '省份' && 'text-[#000000]',
                          )}>
                          |
                        </div>
                        {activeCity.label && (
                          <>
                            <div
                              onClick={(e) => {
                                e.stopPropagation()
                                handleAddressDropdown('city')
                              }}
                              className={cn([
                                'font-miSansRegular330',
                                addressSelect &&
                                currentAddress.label === activeCity.label &&
                                activeCity.label !== '城市'
                                  ? 'text-[#DA291C]'
                                  : activeCity.label !== '城市' && 'text-[#000000]',
                                'mx-[4px]',
                                activeProvince.id ? 'cursor-pointer' : 'opacity-50',
                              ])}>
                              {activeCity.label}
                            </div>
                            <div
                              className={mergeStyles(
                                activeCity.label !== '城市' && 'text-[#000000]',
                              )}>
                              |
                            </div>
                          </>
                        )}
                        {activeDistrict.label && (
                          <div
                            onClick={(e) => {
                              e.stopPropagation()
                              handleAddressDropdown('district')
                            }}
                            className={cn([
                              'font-miSansRegular330',
                              addressSelect &&
                              currentAddress.label === activeDistrict.label &&
                              activeDistrict.label !== '县区'
                                ? 'text-[#DA291C]'
                                : activeDistrict.label !== '县区' && 'text-[#000000]',
                              'ml-[4px]',
                              activeProvince.id && activeCity.id ? 'cursor-pointer' : 'opacity-50',
                            ])}>
                            {activeDistrict.label}
                          </div>
                        )}
                      </div>
                    )}
                    <div className="pointer-events-none">
                      <IconArrow color="#000000" size={24} rotate={addressSelect ? 180 : 0} />
                    </div>
                  </div>

                  {addressSelect && addressList.length > 0 && (
                    <div
                      className="absolute left-0 top-[56px] z-10 w-full rounded-base bg-white p-[24px]"
                      style={{
                        boxShadow: '0px 0px 32px 0px rgba(0, 0, 0, 0.1)',
                      }}>
                      <div className="flex flex-wrap gap-x-[12px]">
                        {addressList.map((item) => (
                          <div
                            key={item.id}
                            onClick={() => handleAddressSelect(item)}
                            className={mergeStyles([
                              'w-[60px] cursor-pointer truncate font-miSansMedium380 text-[14px] leading-[30px] hover:text-primary',
                              `${currentAddress.id === item.id ? 'text-[#DA291C]' : 'text-[#000000]'}`,
                            ])}>
                            {item.label}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Form.Item>

              {/* 详细地址 */}
              <Form.Item
                name="street"
                label={
                  <span className="font-miSansDemiBold450 text-[16px] leading-[120%] text-[#0F0F0F]">
                    详细地址 <span className="text-[#DA291C]">*</span>
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: (
                      <div className="flex items-center gap-[4px]">
                        <TipIcon />
                        请输入详细地址
                      </div>
                    ),
                  },
                ]}>
                <Input
                  placeholder="请输入详细地址"
                  className="h-[48px] rounded-[8px] border-[#D1D1D4]"
                />
              </Form.Item>

              {/* 设置为默认地址 */}
              <Form.Item name="is_default" valuePropName="checked">
                <Checkbox className="font-miSansRegular330 text-[14px] leading-[140%] text-[#0F0F0F]">
                  设置为默认收货地址
                </Checkbox>
              </Form.Item>

              {/* 标签选择 */}
              {/* <div className="space-y-[12px] pt-[12px]">
              <div className="font-miSansDemiBold450 text-[16px] leading-[140%] text-[#0F0F0F]">
                标签
              </div>
              <div className="flex flex-wrap items-center gap-[8px]">
                {ADDRESS_TAGS.map((tag) => (
                  <button
                    key={tag.value}
                    type="button"
                    onClick={() => handleTagSelect(tag.value)}
                    className={`flex h-[40px] w-[90px] items-center justify-center rounded-[4px] border-0 px-[8px] py-[8px] font-miSansMedium380 text-[16px] leading-[120%] ${
                      selectedTag === tag.value
                        ? 'bg-[#DA291C] text-white'
                        : 'bg-[#F3F3F4] text-[#000000] hover:bg-[#E1E1E4]'
                    }`}>
                    {tag.label}
                  </button>
                ))}

                //自定义标签
                <div className="flex items-center gap-[4px] rounded-[8px] bg-[#F3F3F4] px-[12px] py-[8px]">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path
                      d="M6 1V11M1 6H11"
                      stroke="#000000"
                      strokeWidth="1.33"
                      strokeLinecap="round"
                    />
                  </svg>
                  {isCustomTag ? (
                    <Input
                      value={customTag}
                      onChange={(e) => setCustomTag(e.target.value)}
                      placeholder="自定义"
                      className="border-0 bg-transparent p-0 font-miSansMedium380 text-[16px] leading-[120%]"
                      style={{ boxShadow: 'none' }}
                    />
                  ) : (
                    <button
                      type="button"
                      onClick={handleCustomTag}
                      className="border-0 bg-transparent font-miSansMedium380 text-[16px] leading-[120%] text-[#000000]">
                      自定义
                    </button>
                  )}
                </div>
              </div>
            </div> */}
            </Form>
          </div>
        </LocalToastProvider>
      </CustomDrawer>
    </>
  )
}

// 导出内部组件作为主组件
export default AddressEditContent
