'use client'

import { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react'
// import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  generateOSSUrl,
  // IconPlus,
  Membership,
  OrderDetail,
  OrderDetailItem,
  // mergeStyles,
  // NCoinView,
  OrderDisabledReturnProducts,
  OrderMigratedDetail,
  OrderReturnProducts,
  OrderShipmentTracking,
  OrderTracking,
  OrderVouchers,
  PaymentMethod,
  // Price,
  resolveCatchMessage,
  ROUTE,
  setReturnAddressId,
  setReturnId,
  sleep,
  useClipboard,
  useDebounceFn,
  useGetOrderDetailQuery,
  useLazyGetOrderLogisticsQuery,
  useLazyGetOrderRequisitionListQuery,
  useLoadingContext,
  useOrderDetail as useOrderDetailCore,
  userOrderIdSelector,
  useToastContext,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { TToastProps } from '@ninebot/core/src/components/toast/Toast'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
// import { useRouter } from '@/i18n/navigation'

interface OrderDetailContextType {
  isSkinSound: boolean
  orderData: OrderDetail | null | undefined
  isLoading: boolean
  isMigrated: boolean
  migrationOrder: OrderMigratedDetail | null
  copyToClipboard: (text: string) => void
  getI18nString: (key: string, params?: Record<string, string>) => string
  hasNCoin: boolean
  isOnlyNCoin: boolean
  toast: {
    show: (params: TToastProps) => void
    hide: () => void
  }
  loading: {
    show: () => void
    hide: () => void
  }
  currentTime: number
  refetch: () => void
  handleReturnOrder: () => void
  isTracking: boolean
  handleOpenInstruction: (html: string) => void
  setCouponList: (list: OrderVouchers) => void
  setCouponVisible: (visible: boolean) => void
  setMembershipList: (list: Membership[]) => void
  setMembershipVisible: (visible: boolean) => void
  setMembershipProductInfo: (productInfo: OrderDetailItem | null) => void
  isFree: boolean
  showShippingAddress: boolean
  generateOSSUrl: (url: string) => string
  isUnPaid: boolean
  isAllVirtualProduct: boolean | undefined
  migrationProductCount: number
  migrationTotalNCoin: number
  setPickupVisible: (visible: boolean) => void
  returnProducts: OrderReturnProducts
  setReturnProducts: (products: OrderReturnProducts) => void
  returnVisible: boolean
  setReturnVisible: (visible: boolean) => void
  instructionVisible: boolean
  setInstructionVisible: (visible: boolean) => void
  instructionData: string
  couponList: OrderVouchers
  couponVisible: boolean
  pickupVisible: boolean
  membershipList: Membership[]
  membershipVisible: boolean
  membershipProductInfo: OrderDetailItem | null
  disabledProducts: OrderDisabledReturnProducts
  openLogisticsPopup: (track: OrderShipmentTracking) => void
  logisticsData: OrderTracking | null | undefined
  logisticsVisible: boolean
  paymentVisible: boolean
  paymentsList: PaymentMethod[]
  setPaymentVisible: (visible: boolean) => void
  setPaymentsList: (list: PaymentMethod[]) => void
  returnApplicationVisible: boolean
  setReturnApplicationVisible: (visible: boolean, isFromReturnPopup?: boolean) => void
  isFromReturnPopup: boolean
  hasBothDiscounts: boolean
  isShowAddressByShippingMethod: boolean
}

const OrderDetailContext = createContext<OrderDetailContextType | null>(null)

export function OrderDetailProvider({
  orderNumber,
  children,
}: {
  orderNumber: string
  children: React.ReactNode
}) {
  const orderId = useAppSelector(userOrderIdSelector)
  // const searchParams = useSearchParams()
  const getI18nString = useTranslations('Common')
  // const router = useRouter()
  const loading = useLoadingContext()
  const toast = useToastContext()
  const { openPage } = useNavigate()
  const { copyToClipboard } = useClipboard()

  const dispatch = useAppDispatch()

  // const [paymentExpand, setPaymentExpand] = useState(false)

  // const [storeVisible, setStoreVisible] = useState(false)

  const [paymentVisible, setPaymentVisible] = useState(false)
  const [paymentsList, setPaymentsList] = useState<PaymentMethod[]>([])

  const [pickupVisible, setPickupVisible] = useState(false)

  const [couponList, setCouponList] = useState<OrderVouchers>([])
  const [couponVisible, setCouponVisible] = useState(false)

  const [logisticsData, setLogisticsData] = useState<OrderTracking>()
  const [logisticsVisible, setLogisticsVisible] = useState(false)

  const [instructionData, setInstructionData] = useState('')
  const [instructionVisible, setInstructionVisible] = useState(false)

  const [returnVisible, setReturnVisible] = useState(false)
  const [returnProducts, setReturnProducts] = useState<OrderReturnProducts>([])
  const [disabledProducts, setDisabledProducts] = useState<OrderDisabledReturnProducts>([])

  const [membershipList, setMembershipList] = useState<Membership[]>([])
  const [membershipVisible, setMembershipVisible] = useState(false)
  const [membershipProductInfo, setMembershipProductInfo] = useState<OrderDetailItem | null>(null)

  const [returnApplicationVisible, setReturnApplicationVisible] = useState(false)
  const [isFromReturnPopup, setIsFromReturnPopup] = useState(false)

  const [getOrderLogistics] = useLazyGetOrderLogisticsQuery()
  const [getOrderRequisition] = useLazyGetOrderRequisitionListQuery()

  const {
    data: response,
    isLoading,
    isFetching,
    refetch,
  } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderNumber || (orderId as string) },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )

  const orderData = response?.customer?.orders?.items?.[0]

  const currentTime = useRef(new Date().getTime())?.current

  const {
    isMigrated,
    migrationOrder,
    migrationProductCount,
    migrationTotalNCoin,
    showShippingAddress,
    isTracking,
    hasNCoin,
    isOnlyNCoin,
    isFree,
    isAllVirtualProduct,
    isReturnInBatches,
    isUnPaid,
    isSkinSound,
    isShowAddressByShippingMethod,
  } = useOrderDetailCore(orderData)

  /**
   * 是否同时存在N币和现金折扣
   */
  const hasBothDiscounts = useMemo(
    () =>
      Number(orderData?.total?.discounts?.[0]?.distribute_amount) > 0 &&
      Number(orderData?.total?.discounts?.[0]?.distribute_ncoin) > 0,
    [orderData?.total?.discounts],
  )

  /**
   * 查询物流信息
   */
  const openLogisticsPopup = (track: OrderShipmentTracking) => {
    if (track?.carrier_code && track?.track_number && orderData?.encrypt?.nid) {
      loading.show()
      getOrderLogistics({
        company: track.carrier_code,
        trackNo: track.track_number,
        orderNumber: orderData.encrypt.nid,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (Number(res?.getOrderTracking?.length) > 0) {
            setLogisticsData(res?.getOrderTracking?.[0])
            setLogisticsVisible(true)
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  }

  /**
   * 申请退款退货
   */
  const { run: handleReturnOrder } = useDebounceFn(() => {
    if (isReturnInBatches) {
      if (orderData?.encrypt?.nid) {
        loading.show()
        getOrderRequisition({
          orderNumber: orderData.encrypt.nid,
        })
          .unwrap()
          .then(async (res) => {
            loading.hide()
            await sleep(500)
            if (Number(res?.orderRequisitionList?.items?.length) > 0) {
              setReturnProducts(res?.orderRequisitionList?.items)
            }
            if (Number(res?.orderRequisitionList?.requested_items?.length) > 0) {
              setDisabledProducts(res?.orderRequisitionList?.requested_items)
            }
            setReturnVisible(true)
          })
          .catch(async (error) => {
            loading.hide()
            await sleep(500)
            toast.show({
              icon: 'fail',
              content: resolveCatchMessage(error) as string,
            })
          })
      } else {
        toast.show({
          icon: 'fail',
          content: getI18nString('fetch_data_error'),
        })
      }
    } else {
      dispatch(setReturnId(orderData?.encrypt?.nid || ''))
      dispatch(setReturnAddressId(orderData?.shipping_address?.address_id || ''))
      // 使用抽屉展示退货申请表单，而不是导航到新页面
      setReturnApplicationVisible(true)
    }
  })

  /**
   *  查看使用说明
   */
  const handleOpenInstruction = (html: string) => {
    setInstructionData(html)
    setInstructionVisible(true)
  }

  useEffect(() => {
    if (!isLoading && isFetching) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, isLoading, isFetching])

  /**
   * 自定义返回按钮操作事件
   */
  // const handleNavLeftPress = useCallback(() => {
  //   const from = searchParams.get('from') || ''
  //   // 从以下页面到当前页面的流程，返回到首页
  //   if (
  //     [
  //       ROUTE.checkoutPending,
  //       ROUTE.checkoutSuccess,
  //       ROUTE.checkoutFail,
  //       ROUTE.checkoutCart,
  //     ].includes(from)
  //   ) {
  //     router.replace('/')
  //   }

  //   // 默认返回行为
  //   router.back()
  // }, [router, searchParams])

  useEffect(() => {
    // 如果没拿到订单数据，跳到订单列表
    if (!isLoading && !orderData) {
      openPage({
        route: ROUTE.accountOrder,
      })
    }
  }, [isLoading, orderData, openPage])

  const value = {
    isSkinSound,
    orderData,
    isLoading,
    isMigrated,
    migrationOrder,
    copyToClipboard,
    getI18nString,
    hasNCoin,
    isOnlyNCoin,
    toast,
    loading,
    currentTime,
    paymentVisible,
    paymentsList,
    setPaymentVisible,
    setPaymentsList,
    refetch,
    handleReturnOrder,
    isTracking,

    handleOpenInstruction,
    setCouponList,
    setCouponVisible,
    setMembershipList,
    setMembershipVisible,
    setMembershipProductInfo,
    isFree,
    showShippingAddress,
    generateOSSUrl,

    isUnPaid,
    isAllVirtualProduct,
    migrationProductCount,
    migrationTotalNCoin,
    setPickupVisible,

    returnProducts,
    setReturnProducts,
    returnVisible,
    setReturnVisible,
    instructionVisible,
    setInstructionVisible,
    instructionData,
    couponList,
    couponVisible,
    pickupVisible,
    membershipList,
    membershipVisible,
    membershipProductInfo,
    disabledProducts,
    openLogisticsPopup,
    logisticsData,
    logisticsVisible,
    returnApplicationVisible,
    setReturnApplicationVisible: (visible: boolean, isFromReturnPopup = false) => {
      setReturnApplicationVisible(visible)
      setIsFromReturnPopup(isFromReturnPopup)
    },
    isFromReturnPopup,
    hasBothDiscounts,
    isShowAddressByShippingMethod,
  }

  return <OrderDetailContext.Provider value={value}>{children}</OrderDetailContext.Provider>
}

export function useOrderDetail() {
  const context = useContext(OrderDetailContext)
  if (!context) {
    throw new Error('useOrderDetail must be used within a OrderDetailProvider')
  }
  return context
}
