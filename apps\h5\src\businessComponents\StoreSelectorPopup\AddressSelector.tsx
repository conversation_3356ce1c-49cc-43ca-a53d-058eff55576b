'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  resolveCatchMessage,
  TCatchMessage,
  useLazyGetProvinceCityCountyQuery,
  useToastContext,
} from '@ninebot/core'
import { Tabs } from 'antd-mobile'

import { Skeleton } from '@/components'

// ==================== 类型定义 ====================

/**
 * 地区数据项接口
 */
interface AreaItem {
  label: string
  id: string
}

/**
 * API返回数据类型
 */
interface ApiAreaItem {
  default_name: string
  region_id?: string
  city_id?: string
  district_id?: string
}

/**
 * 组件属性接口
 */
interface AddressSelectorProps {
  defaultValue?: {
    province?: string
    city?: string
    district?: string
  }
  onSelect?: (province: AreaItem, city: AreaItem, district: AreaItem) => void
}

/**
 * 地址类型
 */
type AddressType = 'region' | 'city'

/**
 * Tab类型枚举
 */
enum TabType {
  PROVINCE = 'province',
  CITY = 'city',
  DISTRICT = 'district',
}

// ==================== 常量定义 ====================

/**
 * 默认空值
 */
const DEFAULT_AREA_ITEM: AreaItem = { label: '', id: '' }

/**
 * 骨架屏配置
 */
const SKELETON_CONFIG = {
  count: 9,
  style: {
    borderRadius: 4,
    height: 32,
    marginTop: 24,
    width: '100%',
    background: '#f5f5f5',
  },
} as const

// ==================== 主组件 ====================

/**
 * 地址选择器组件
 *
 * 功能：
 * 1. 支持省市区三级联动选择
 * 2. 自动加载和缓存地址数据
 * 3. 支持默认值设置
 * 4. 提供友好的加载状态
 */
const AddressSelector = ({ defaultValue, onSelect }: AddressSelectorProps) => {
  const getI18nString = useTranslations('Common')
  const [getAddressData, { isFetching }] = useLazyGetProvinceCityCountyQuery()
  const toast = useToastContext()

  // ==================== 状态管理 ====================

  // Tab状态
  const [activeTab, setActiveTab] = useState<TabType>(TabType.PROVINCE)

  // 选中状态
  const [activeProvince, setActiveProvince] = useState<AreaItem>(DEFAULT_AREA_ITEM)
  const [activeCity, setActiveCity] = useState<AreaItem>(DEFAULT_AREA_ITEM)
  const [activeDistrict, setActiveDistrict] = useState<AreaItem>(DEFAULT_AREA_ITEM)

  // 数据状态
  const [provinces, setProvinces] = useState<AreaItem[]>([])
  const [cities, setCities] = useState<AreaItem[]>([])
  const [districts, setDistricts] = useState<AreaItem[]>([])

  // 控制状态
  const [isDefaultSet, setIsDefaultSet] = useState(true)

  // ==================== 计算属性 ====================

  /**
   * Tab配置
   */
  const tabs = useMemo(
    () => [
      { key: TabType.PROVINCE, title: getI18nString('province') },
      { key: TabType.CITY, title: getI18nString('city') },
      { key: TabType.DISTRICT, title: getI18nString('district') },
    ],
    [getI18nString],
  )

  /**
   * 当前地址列表
   */
  const currentAddressList = useMemo(() => {
    switch (activeTab) {
      case TabType.PROVINCE:
        return provinces
      case TabType.CITY:
        return cities
      case TabType.DISTRICT:
        return districts
      default:
        return []
    }
  }, [activeTab, provinces, cities, districts])

  /**
   * 当前选中的地址项
   */
  const currentSelectedAddress = useMemo(() => {
    switch (activeTab) {
      case TabType.PROVINCE:
        return activeProvince
      case TabType.CITY:
        return activeCity
      case TabType.DISTRICT:
        return activeDistrict
      default:
        return DEFAULT_AREA_ITEM
    }
  }, [activeTab, activeProvince, activeCity, activeDistrict])

  // ==================== 工具函数 ====================

  /**
   * 格式化API数据为组件数据
   */
  const formatApiData = useCallback(
    (data: ApiAreaItem[], type: AddressType, isCity: boolean = false): AreaItem[] => {
      return data.map((item) => {
        let id = ''
        if (type === 'city') {
          // 获取区县数据时使用district_id
          id = item.district_id || ''
        } else if (type === 'region') {
          if (isCity) {
            // 获取城市数据时使用city_id
            id = item.city_id || ''
          } else {
            // 获取省份数据时使用region_id
            id = item.region_id || ''
          }
        }
        return {
          id,
          label: item.default_name,
        }
      })
    },
    [],
  )

  /**
   * 获取地址数据
   */
  const fetchAddressData = useCallback(
    async ({ id, type }: { id?: string; type: AddressType }) => {
      try {
        // 在开始获取数据时立即清空对应的数据数组，避免显示旧数据
        if (id && type === 'city') {
          // 获取区县数据时，清空区县列表
          setDistricts([])
        } else if (id && type === 'region') {
          // 获取城市数据时，清空城市和区县列表
          setCities([])
          setDistricts([])
        }

        const response = await getAddressData({ FilterValue: id, type }).unwrap()
        const parsedData = JSON.parse(response.province_city_county_search || '[]') as ApiAreaItem[]

        // 根据类型和是否有id参数来判断数据用途
        const isCity = Boolean(id && type === 'region')
        const formattedData = formatApiData(parsedData, type, isCity)

        // 根据类型设置对应的数据
        if (id && type === 'city') {
          setDistricts(formattedData)
        } else if (id && type === 'region') {
          setCities(formattedData)
        } else {
          setProvinces(formattedData)
        }
      } catch (error) {
        console.error('获取地址数据失败:', error)
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })
      }
    },
    [getAddressData, formatApiData, toast],
  )

  /**
   * 设置默认值
   */
  const setDefaultValue = useCallback(
    (list: AreaItem[], value: string | undefined, setter: (item: AreaItem) => void) => {
      if (list.length > 0 && value) {
        const defaultItem = list.find((item) => item.label === value)
        if (defaultItem) {
          setter(defaultItem)
        }
      }
    },
    [],
  )

  // ==================== 事件处理函数 ====================

  /**
   * 处理地址选择
   */
  const handleAddressSelect = useCallback(
    (address: AreaItem) => {
      setIsDefaultSet(false)

      switch (activeTab) {
        case TabType.PROVINCE:
          setActiveProvince(address)
          setActiveCity(DEFAULT_AREA_ITEM)
          setActiveDistrict(DEFAULT_AREA_ITEM)
          setActiveTab(TabType.CITY)
          break
        case TabType.CITY:
          setActiveCity(address)
          setActiveDistrict(DEFAULT_AREA_ITEM)
          setActiveTab(TabType.DISTRICT)
          break
        case TabType.DISTRICT:
          setActiveDistrict(address)
          break
      }
    },
    [activeTab],
  )

  // ==================== 副作用 ====================

  /**
   * 初始化省份数据
   */
  useEffect(() => {
    fetchAddressData({ type: 'region' })
  }, [fetchAddressData])

  /**
   * 监听省份变化，获取城市数据
   */
  useEffect(() => {
    if (activeProvince.id) {
      fetchAddressData({ id: activeProvince.id, type: 'region' })
    }
  }, [fetchAddressData, activeProvince.id])

  /**
   * 监听城市变化，获取区县数据
   */
  useEffect(() => {
    if (activeCity.id) {
      fetchAddressData({ id: activeCity.id, type: 'city' })
    }
  }, [fetchAddressData, activeCity.id])

  /**
   * 设置默认省份
   */
  useEffect(() => {
    setDefaultValue(provinces, defaultValue?.province, setActiveProvince)
  }, [provinces, defaultValue?.province, setDefaultValue])

  /**
   * 设置默认城市
   */
  useEffect(() => {
    setDefaultValue(cities, defaultValue?.city, setActiveCity)
  }, [cities, defaultValue?.city, setDefaultValue])

  /**
   * 设置默认区县
   */
  useEffect(() => {
    setDefaultValue(districts, defaultValue?.district, setActiveDistrict)
  }, [districts, defaultValue?.district, setDefaultValue])

  /**
   * 执行选择回调
   */
  useEffect(() => {
    if (onSelect && !isDefaultSet && activeProvince.id) {
      onSelect(activeProvince, activeCity, activeDistrict)
    }
  }, [onSelect, isDefaultSet, activeProvince, activeCity, activeDistrict])

  // ==================== 渲染函数 ====================

  /**
   * 渲染地址列表项
   */
  const renderAddressItem = useCallback(
    (item: AreaItem) => (
      <div
        key={item.id}
        className={`${item.id === currentSelectedAddress.id ? 'text-primary' : ''} ${
          isFetching ? 'pointer-events-none' : 'cursor-pointer'
        }`}
        onClick={() => !isFetching && handleAddressSelect(item)}>
        {item.label}
      </div>
    ),
    [currentSelectedAddress.id, handleAddressSelect, isFetching],
  )

  /**
   * 渲染骨架屏
   */
  const renderSkeleton = useCallback(
    () => (
      <div className="h-[405px] overflow-hidden">
        {isFetching && (
          <div className="py-2">
            {Array(SKELETON_CONFIG.count)
              .fill(null)
              .map((_, index) => (
                <Skeleton key={index} style={SKELETON_CONFIG.style} />
              ))}
          </div>
        )}
      </div>
    ),
    [isFetching],
  )

  /**
   * 渲染地址列表
   */
  const renderAddressList = useCallback(() => {
    if (currentAddressList.length > 0) {
      return (
        <div className="space-y-base-24 pb-36 pt-10 font-miSansRegular330 text-lg">
          {currentAddressList.map(renderAddressItem)}
        </div>
      )
    }
    return renderSkeleton()
  }, [currentAddressList, renderAddressItem, renderSkeleton])

  // ==================== 组件返回 ====================

  return (
    <div className="flex flex-1 flex-col overflow-hidden">
      {/* Tab导航 */}
      <Tabs
        className="pdp-address-tabs px-8"
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabType)}>
        {tabs.map((tab) => (
          <Tabs.Tab key={tab.key} title={tab.title} className="mr-[16px] !flex-none !px-0" />
        ))}
      </Tabs>

      {/* 地址列表 */}
      <div className="flex-1 overflow-y-auto px-8">{renderAddressList()}</div>
    </div>
  )
}

export default AddressSelector
