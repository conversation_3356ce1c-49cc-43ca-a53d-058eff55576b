import React from 'react'
import { useTranslations } from 'next-intl'
import { generateOSSUrl, Membership } from '@ninebot/core'
import { Image } from 'antd-mobile'

import { CustomPopup } from '@/components'

type MembershipPopupProps = {
  popupVisible: boolean
  copyToClipboard: (text: string) => void
  closePopup: () => void
  data: Membership[]
}

/**
 * 数字会员弹窗
 */
const MembershipPopup = ({
  popupVisible,
  copyToClipboard,
  closePopup,
  data,
}: MembershipPopupProps) => {
  const getI18nString = useTranslations('Common')

  /**
   * 券码状态
   */
  const renderStatus = (item: Membership) => {
    if (!item) return null

    const { status, code, status_label } = item

    if (status === '0') {
      return (
        <div
          className="mr-[12px] flex h-[32px] w-[50px] cursor-pointer flex-row items-center justify-center rounded-[101px] border-[1px] border-[#DA291C]"
          onClick={() => copyToClipboard(code || '')}>
          <div className="text-[13px] leading-[16px] text-[#DA291C]">
            {getI18nString('coupon_code_copy')}
          </div>
        </div>
      )
    } else {
      return (
        <div>
          <Image
            style={{
              top: 10,
              right: 0,
              height: 80,
              width: 80,
            }}
            src={generateOSSUrl('/icons/coupon_code_info_status.png')}
            alt=""
          />
          <div className="right-[-23px] top-[26px] -rotate-[40deg] transform font-miSansRegular330 text-[14px] leading-[19px] text-[#BBBBBD]">
            {status_label}
          </div>
        </div>
      )
    }
  }

  return (
    <CustomPopup
      visible={popupVisible}
      onClose={closePopup}
      bodyStyle={{
        backgroundColor: '#F3F3F4',
      }}
      contentClassName="px-base-12"
      showHeader
      headerClassName="!border-none"
      headTitle={getI18nString('coupon_code')}>
      <div className="mt-[12px] max-h-[360px] flex-1">
        {data.map((item, index) => (
          <div
            key={index}
            className="mb-[12px] flex h-[96px] items-center justify-between overflow-hidden rounded-[12px] bg-white">
            <div className="px-base-5 relative flex h-full w-[90px] items-center justify-center">
              <div className="absolute -right-[6px] -top-[6px] h-[12px] w-[12px] rounded-full bg-[#F3F3F4]" />
              <div className="flex flex-1 flex-row items-center justify-center">
                <div
                  className={`text-[16px] leading-[22px] text-[#000000] ${item?.status === 'used' ? 'text-[#86868B]' : ''}`}>
                  {getI18nString('coupon_index', { key: index + 1 })}
                </div>
              </div>
              <div className="absolute -bottom-[6px] -right-[6px] h-[12px] w-[12px] rounded-full bg-[#F3F3F4]" />
            </div>
            <div className="h-[62px] w-[1px] border-l border-dashed border-[#E1E1E4] bg-transparent" />
            <div className="flex-1">
              <div className="flex flex-1 justify-between">
                <div className="ml-[12px] flex flex-1 flex-row items-center justify-between">
                  <div>
                    <div
                      className={`mb-[5px] max-w-[180px] truncate font-miSansDemiBold450 text-[16px] leading-[21px] text-[#000000] ${item?.status !== '0' ? 'text-[#86868B]' : ''}`}>
                      {item?.code}
                    </div>
                    <div className="text-[12px] leading-[14px] text-[#86868B]">
                      {getI18nString('coupon_code_expires', {
                        key: item?.expired_at?.split(' ')[0],
                      })}
                    </div>
                  </div>
                  {renderStatus(item)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </CustomPopup>
  )
}

export default MembershipPopup
