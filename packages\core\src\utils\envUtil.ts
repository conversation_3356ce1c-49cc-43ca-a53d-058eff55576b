/* ------------------------------- 获取env信息辅助函数 ------------------------------- */

/**
 * 获取当前应用模式
 */
export const getAppMode = () => {
  return process.env.NEXT_PUBLIC_MODE
}

/**
 * 获取商城 Api URL
 */
export const getShopApiUrl = () => {
  return process.env.NEXT_PUBLIC_SHOP_API_URL
}

/**
 * 获取九号 Api URL
 */
export const getNineBotApiUrl = () => {
  return process.env.NEXT_PUBLIC_NINEBOT_API_URL
}

/**
 * 获取商城 Api URL 的认证信息
 */
export const getShopApiUrlBasicAuth = () => {
  return process.env.NEXT_PUBLIC_SHOP_API_URL_BASIC_AUTH
}

/**
 * 获取 OSS URL
 */
export const getOSSUrl = () => {
  return process.env.NEXT_PUBLIC_SHOP_OSS_URL
}

/**
 * 获取当前应用版本
 */
export const getAppVersion = () => {
  return process.env.BUILD_VERSION || ''
}

/**
 * 获取密钥
 */
export const getPublicKey = () => {
  return process.env.PUBLIC_KEY
}

/**
 * 判断当前应用是 Web 还是 H5
 * 根据运行时路径判断当前是哪个应用
 */
export const getPlatformType = () => {
  if (typeof window === 'undefined') {
    // 服务端渲染时，根据执行路径判断
    try {
      const appPath = process.cwd()
      if (appPath.includes('/apps/web') || appPath.includes('\\apps\\web')) {
        return 'ninebot_web'
      }
      if (appPath.includes('/apps/h5') || appPath.includes('\\apps\\h5')) {
        return 'ninebot_h5'
      }
    } catch {
      // 如果出错，默认返回 web
      return 'ninebot_web'
    }
  } else {
    // 客户端渲染时，根据 URL 的 user agent 或其他客户端特征来判断
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    )
    return isMobile ? 'ninebot_h5' : 'ninebot_web'
  }

  // 默认返回 web
  return 'ninebot_web'
}

/**
 * 判断当前应用模式
 */
export const isAppMode = (mode: string) => {
  return getAppMode() === mode
}

/**
 * 获取当前应用是否处于生产模式
 */
export const isAppProd = () => isAppMode('production')

/**
 * 获取当前应用是否处于开发模式
 */
export const isAppDev = () => isAppMode('development')

/**
 * 获取当前应用是否处于开发模式
 */
export const isAppStaging = () => isAppMode('staging')
