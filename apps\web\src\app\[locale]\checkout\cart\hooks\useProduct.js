import { useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  useGetProductDetailQuery,
  useToastContext,
  useUpdateCartOptionsMutation,
} from '@ninebot/core'
import {
  getConfigAttr,
  getConfigPrice,
  getIsAllOutOfStock,
  getIsOutOfStock,
  getOutOfStockVariants,
  getProductConfigurableOptions,
  getProductInfo,
  isProductConfigurable,
} from '@ninebot/core/src/utils/productUtils'
import { resolveCatchMessage, sleep } from '@ninebot/core/src/utils/util'
import cloneDeep from 'lodash-es/cloneDeep'

export function useProduct(productStatus, productApi, handleOptionsSwitch, handleRefresh) {
  const [selectStore, setSelectStore] = useState(null)
  const [doorVisible, setDoorVisible] = useState(false)

  const toast = useToastContext()
  const getI18nString = useTranslations('Common')

  const { sku, parentSku, product } = productStatus
  const [show, setShow] = useState(false)

  const [isLoading, setIsLoading] = useState(false)
  const [productDetail, setCurConfigProduct] = useState(null)
  const [productConfigurableOptions, setCurConfigConfigurableOptions] = useState([])

  const { currentData } = useGetProductDetailQuery(
    {
      filter: {
        sku: {
          eq: parentSku,
        },
      },
    },
    {
      skip: !parentSku,
      refetchOnMountOrArgChange: true,
    },
  )

  const [updateCartOptions] = useUpdateCartOptionsMutation()

  const isOutOfStockProductDisplayed = (productDetails) => {
    if (productDetails.configurable_options) {
      let totalVariants = 1
      for (const option of productDetails.configurable_options) {
        const length = option.values.length
        totalVariants = totalVariants * length
      }

      return productDetails.variants.length === totalVariants
    }
  }

  // 门店自提
  const deliveryMethodPickup = useMemo(() => {
    let flag = false
    if (productDetail) {
      const res = productDetail.custom_attributesV3.items.find(
        (item) => item.code === 'delivery_method',
      )

      if (res) {
        const res1 = res.selected_options.find(
          (item) => item.value === 'delivery_method_store_pickup',
        )
        if (res1) {
          flag = true
        }
      }

      return flag
    }
  }, [productDetail])

  const getStatusStock = () => {
    return !(productStatus.isEverythingOutOfStock || productStatus.isOutOfStock)
  }

  const handleProductConfigurable = (
    optionCodes,
    nextOptionSelections,
    nextSingleOptionSelection,
    products,
    isFirst,
  ) => {
    const productDetails = products || productDetail

    const price = getConfigPrice(productDetails, optionCodes, nextOptionSelections)

    const configInfo = getConfigAttr(productDetails, optionCodes, nextOptionSelections)

    const configName = configInfo.attributes.map((item) => {
      return item.label
    })

    const selected_options = []
    const selected_configurable_option = configInfo.attributes.map((item) => {
      selected_options.push(item.value_index)
      return {
        attribute_code: item.code,
        value_uid: item.uid,
      }
    })

    const info = getProductInfo(productDetails, optionCodes, nextOptionSelections)

    const isEverythingOutOfStock = getIsAllOutOfStock(productDetails)
    const isOutOfStock = getIsOutOfStock(productDetails, optionCodes, nextOptionSelections)

    let outOfStockVariants = getOutOfStockVariants(
      productDetails,
      optionCodes,
      nextSingleOptionSelection,
      nextOptionSelections,
      isOutOfStockProductDisplayed(productDetails),
    )

    if (isFirst && isOutOfStock) {
      // 修正自身无货
      outOfStockVariants = outOfStockVariants.concat(selected_options)
    }

    // 安装服务
    let servicePrice = 0
    let serviceItem = null
    const extensionInfo = productStatus.extensionInfo

    if (
      extensionInfo &&
      extensionInfo.custom_options &&
      extensionInfo.custom_options.length > 0 &&
      isFirst
    ) {
      configInfo.options.forEach((option) => {
        option.dropDown.forEach((item) => {
          servicePrice = item.price
          serviceItem = [
            {
              value: item.uid,
              sku: item.sku,
              title: item.title,
              option_id: option.option_id,
              price: item.price,
            },
          ]
        })
      })
    }

    // 门店信息
    const store_info = extensionInfo.store_info
    if (extensionInfo && store_info) {
      setSelectStore(store_info)
    } else {
      setSelectStore(null)
    }

    // payment_method_ncoin 展示展示n样式
    // payment_method_cash_ncoin 且最大n币数有值 展示现金加最大n币数
    // 展示正常价格
    const maxUsagencoins = configInfo.product.custom_attributesV3.items.find(
      (item) => item.code === 'max_usage_limit_ncoins',
    )
    let selectArr = [...configName]
    if (serviceItem) {
      selectArr = [...selectArr, serviceItem[0].title]
    }
    const oldSku = isFirst
      ? {
          oldSku: info.sku,
          oldStore: extensionInfo && store_info ? store_info.store_name : '',
        }
      : {}
    productApi({
      ...productStatus,
      optionCodes,
      optionSelections: nextOptionSelections,
      price,
      selectOptionName: selectArr.join(' '),
      image: info.image,
      id: info.id,
      sku: info.sku,
      ...oldSku,
      parent_sku: productDetails.sku,
      selected_configurable_option,
      servicePrice,
      serviceItem,
      maxUsagencoins,
      productItem: info.productItem,
      isEverythingOutOfStock,
      isOutOfStock,
      outOfStockVariants,
    })

    // 重置门店选择
    if (!isFirst) {
      setSelectStore(null)
    }

    setShow(true)
  }

  // 切换规格
  const handleSelectionChange = (item, optionId) => {
    const { optionCodes, optionSelections } = productStatus
    const selection = item.value_index

    const nextOptionSelections = new Map([...optionSelections])
    nextOptionSelections.set(optionId, selection)

    const nextSingleOptionSelection = new Map()
    nextSingleOptionSelection.set(optionId, selection)
    handleProductConfigurable(optionCodes, nextOptionSelections, nextSingleOptionSelection)
  }

  const handleQtyChange = (quantity) => {
    productApi({
      ...productStatus,
      quantity,
    })
  }

  // 换购
  const handleAddCart = async () => {
    const productInfo = cloneDeep(product)

    const {
      sku: curSku,
      item_id,
      parent_sku,
      selected_configurable_option,
      serviceItem,
      productItem,
      extensionInfo,
      quantity,
    } = productStatus

    if (quantity > productItem.salable_qty) {
      productApi({
        ...productStatus,
        sku: '',
        parentSku: '',
        popVisible: false,
      })
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: getI18nString('cart_stock_tip'),
      })
      return
    }

    // 判断选择items.options是否是必选的
    let isOptionsSelected = false
    let optionsSelectedTitle = ''
    productDetail?.options?.forEach(function (item) {
      if (item.required) {
        isOptionsSelected = true
        optionsSelectedTitle = item.title
      }
    })
    if (isOptionsSelected && !serviceItem) {
      toast.show({
        icon: 'info',
        content: '请' + optionsSelectedTitle,
      })
      return
    }
    // 服务包
    const serviceOptionItem = []
    if (serviceItem) {
      serviceItem.forEach((item) => {
        serviceOptionItem.push({
          value: item.value,
          option_id: item.option_id,
        })
      })
    }

    // 门店自提
    let selectedStoreInfo = {}
    productInfo.extension_info = productInfo.extension_info || {}
    if (extensionInfo.store_info) {
      if (selectStore) {
        selectedStoreInfo = {
          selected_store_info: {
            store_id: selectStore.store_id,
            store_code: selectStore.store_code,
          },
        }
        productInfo.extension_info.store_info = selectStore
      } else {
        handleViewStorePop()
        return
      }
    }

    productInfo.product = productItem
    if (isProductConfigurable(productDetail) && selected_configurable_option) {
      const config_selected_info = []
      selected_configurable_option.forEach((item) => {
        productDetail.configurable_options.forEach((i) => {
          if (i.attribute_code === item.attribute_code) {
            const val = i.values.find((j) => j.uid === item.value_uid)
            config_selected_info.push({
              attribute_label: i.label,
              value_label: val.label,
            })
          }
        })
      })

      if (config_selected_info.length > 0) {
        productInfo.extension_info.config_selected_info = config_selected_info
      }
    }

    const input = {
      parent_sku,
      sku: curSku,
      item_id,
      selected_configurable_option,
      custom_options: serviceOptionItem,
      ...selectedStoreInfo,
    }

    try {
      setIsLoading(true)
      const response = await updateCartOptions({
        input,
      }).unwrap()

      if (response.updateCartProductOptions && response.updateCartProductOptions.status) {
        // 关闭弹窗
        productApi({
          ...productStatus,
          sku: '',
          parentSku: '',
          popVisible: false,
        })

        // 重新获取购物车数据以确保url_key和url_suffix是最新的
        if (handleRefresh) {
          await handleRefresh()
        } else {
          // 降级处理：如果没有handleRefresh，仍使用原来的方式
          handleOptionsSwitch(productInfo)
        }

        await sleep(500)
      }
      setIsLoading(false)
    } catch (error) {
      // 关闭
      productApi({
        ...productStatus,
        sku: '',
        parentSku: '',
        popVisible: false,
      })
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error),
      })
      setIsLoading(false)
    }
  }

  // 打开选择门店
  const handleViewStorePop = async () => {
    productApi({
      ...productStatus,
      popVisible: false,
    })
    setDoorVisible(true)
  }

  const handlePopupClose = () => {
    setShow(false)
    setCurConfigProduct(null)
    productApi({
      ...productStatus,
      sku: '',
      parentSku: '',
      popVisible: false,
    })
  }

  const safeguardItems = useMemo(() => {
    let arr = []
    if (productDetail) {
      const res = productDetail.custom_attributesV3.items.find(
        (item) => item.code === 'safeguard_clause',
      )
      if (res) {
        arr = res.selected_options
      }
    }
    return arr
  }, [productDetail])

  const productServiceOptions = useMemo(() => {
    return productDetail ? (productDetail.options ?? []) : []
  }, [productDetail])

  const handleUpdateService = (item) => {
    if (productStatus.serviceItem) {
      productApi({
        ...productStatus,
        servicePrice: 0,
        serviceItem: null,
      })
    } else {
      productApi({
        ...productStatus,
        servicePrice: item.price,
        serviceItem: [item],
      })
    }
  }

  useEffect(() => {
    if (currentData) {
      const optionCodes = new Map()
      const nextOptionSelections = new Map()
      const nextSingleOptionSelection = new Map()

      const products = currentData.products.items[0]

      if (!products) {
        sleep(1500)
        productApi({
          ...productStatus,
          sku: '',
          parentSku: '',
          popVisible: false,
        })
        return
      }
      setCurConfigProduct(products)

      if (isProductConfigurable(products)) {
        const currentProduct = products.variants.find((item) => item.product.sku === sku)

        const productOptions = getProductConfigurableOptions(products)
        setCurConfigConfigurableOptions(productOptions)

        for (const option of productOptions) {
          optionCodes.set(option.attribute_id, option.attribute_code)
        }

        currentProduct.attributes.forEach((item) => {
          productOptions.forEach((option) => {
            option.values.forEach((i) => {
              if (item.value_index === i.value_index) {
                nextSingleOptionSelection.clear()
                nextOptionSelections.set(option.attribute_id, i.value_index)
                nextSingleOptionSelection.set(option.attribute_id, i.value_index)
              }
            })
          })
        })

        handleProductConfigurable(
          optionCodes,
          nextOptionSelections,
          nextSingleOptionSelection,
          products,
          true,
        )
      } else {
        setCurConfigConfigurableOptions([])
        handleProductConfigurable(
          optionCodes,
          nextOptionSelections,
          nextSingleOptionSelection,
          products,
          true,
        )
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentData])

  const isEnablebtn = () => {
    const store_name = selectStore ? selectStore.store_name : ''
    let flag = false
    if (isLoading) {
      flag = true
    } else if (
      productStatus.oldSku === productStatus.sku &&
      productStatus.oldStore === store_name
    ) {
      flag = true
    } else {
      flag = false
    }

    return flag
  }
  return {
    selectStore,
    show,
    productDetail,
    productConfigurableOptions,
    safeguardItems,
    productServiceOptions,
    deliveryMethodPickup,
    doorVisible,
    setDoorVisible,
    setSelectStore,
    handleSelectionChange,
    handleQtyChange,
    handlePopupClose,
    getStatusStock,
    isEnablebtn,
    handleAddCart,
    handleUpdateService,
    handleViewStorePop,
  }
}
