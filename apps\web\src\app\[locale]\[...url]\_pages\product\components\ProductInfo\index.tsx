import React from 'react'
import type { StoreListItem } from '@ninebot/core'
import { IconStoreTag, useAuth } from '@ninebot/core'
import { isProductConfigurable } from '@ninebot/core/src/utils/productUtils'

import { InstallationService } from '@/businessComponents'
import {
  OptionItem,
  ProductConfigurableOption,
  ProductDetails,
  ProductDropDownItem,
  ProductServiceParams,
  ProductStatus,
  SafeguardItem,
  Variant,
} from '@/types/product'

import { useProduct } from '../../context/ProductContext'
import StoreSelector from '../StoreSelector'

import BasicServiceList from './BasicServiceList'
import ColorSelector from './ColorSelector'
import CompatibleProducts from './CompatibleProducts'
import ProductCoupon from './ProductCoupon'
import ProductPrice from './ProductPrice'
import ServicePolicy from './ServicePolicy'
import SizeSelector from './SizeSelector'

interface ProductInfoProps {
  showFixedContent?: boolean
}

const ProductInfo = ({ showFixedContent = true }: ProductInfoProps) => {
  const {
    productDetails: product,
    productStatus,
    safeguardItems,
    deliveryMethodPickup,
    productConfigurableOptions,
    variants,
    handleSelectionChange,
    isBoundCart,
    setDoorVisible,
    selectStore,
    productConfigOptions,
    handleUpdateService,
    hasCoupon,
  } = useProduct() as {
    productDetails: ProductDetails
    productStatus: ProductStatus
    safeguardItems: SafeguardItem[]
    deliveryMethodPickup: boolean
    productConfigurableOptions: ProductConfigurableOption[]
    variants: Variant[]
    handleSelectionChange: (item: OptionItem, id: string) => void
    isBoundCart: boolean
    setDoorVisible: (visible: boolean) => void
    selectStore: StoreListItem | null
    productConfigOptions: {
      option_id: string
      title: string
      dropDown: ProductDropDownItem[]
    }[]
    handleUpdateService: (params: ProductServiceParams) => void
    hasCoupon: boolean
  }

  const { isLoggedIn, redirectLoginUrl } = useAuth()

  const handleViewStorePop = () => {
    if (!isLoggedIn) {
      redirectLoginUrl()
      return
    }
    setDoorVisible(true)
  }

  // 获取商品标语
  const productSlogan = product?.custom_attributesV3.items.find(
    (item) => item.code === 'product_slogan',
  )?.value

  // 获取适配产品
  const compatibleProducts = product?.custom_attributesV3.items.find(
    (item) => item.code === 'compatible_product',
  )?.value

  const isShowSafeguard = () => {
    let flag = false
    if (safeguardItems && safeguardItems.length > 0) {
      if (safeguardItems[0].value) {
        flag = true
      }
    }

    return flag
  }

  // 固定内容：商品标题、价格、服务政策
  if (showFixedContent) {
    return (
      <div className="flex flex-1 flex-col gap-base-24">
        {/* 商品标题 */}
        <h1 className="m-0">
          <div className="font-miSansDemiBold450 text-[24px] leading-[28px]">
            <span className="inline-flex items-center">
              {deliveryMethodPickup && <IconStoreTag size={20} />}
            </span>
            <span>{product?.name}</span>
          </div>
        </h1>

        {/* 价格信息 */}
        <div className="min-h-[36px]">
          <ProductPrice />
        </div>
      </div>
    )
  }

  // 可滚动内容：其他所有内容
  return (
    <div className="mt-8 flex flex-1 flex-col gap-base-24">
      {/* 优惠券信息 */}

      {hasCoupon ? <ProductCoupon selectedProductId={Number(product?.id) || 0} /> : null}

      {/* 商品标语 */}
      {productSlogan && (
        <p className="-mt-[12px] font-miSansRegular330 text-[14px] leading-[1.2] text-[#444446]">
          {productSlogan}
        </p>
      )}

      {/* 服务政策 */}
      {isShowSafeguard() ? (
        <div className="border-b border-[#00000014] pb-base-24">
          <ServicePolicy
            data={safeguardItems}
            product={product}
            deliveryMethodPickup={deliveryMethodPickup}
          />
        </div>
      ) : null}

      {/* 适配产品 */}
      {compatibleProducts ? (
        <div className="border-b border-[#00000014] pb-base-24">
          <CompatibleProducts products={compatibleProducts} />
        </div>
      ) : null}

      {/* 基础服务包 */}
      {product?.__typename === 'BundleProduct' ? (
        <div className="border-b border-[#00000014] pb-base-24">
          {' '}
          <BasicServiceList products={product.items} />
        </div>
      ) : null}

      {/* 产品配置选项 */}
      {isProductConfigurable(product) && productConfigurableOptions?.length > 0
        ? productConfigurableOptions.map((option: ProductConfigurableOption) => {
            // 获取当前选中的选项
            const selectedOptionIndex = productStatus?.optionSelections?.get(option.attribute_id)
            const selectedOption = option.values.find(
              (item) => item.value_index === selectedOptionIndex,
            )
            const selectedLabel = selectedOption?.label || ''

            return (
              <div key={option.attribute_id} className="min-h-[88px] pb-base-24">
                <div className="flex flex-col gap-base-16">
                  <div className="font-miSansSemibold520 text-[18px] leading-none">
                    {option.label + '：'}
                    <span className="font-miSansMedium380">{selectedLabel}</span>
                  </div>
                  {option.attribute_code?.includes('color') ? (
                    <ColorSelector
                      optionItems={option.values}
                      id={option.attribute_id}
                      variants={variants}
                      productStatus={productStatus}
                      onSelectionChange={handleSelectionChange}
                    />
                  ) : (
                    <SizeSelector
                      optionItems={option.values}
                      id={option.attribute_id}
                      productStatus={productStatus}
                      onSelectionChange={handleSelectionChange}
                    />
                  )}
                </div>
              </div>
            )
          })
        : null}

      {/* 安装服务 */}
      {productConfigOptions?.length > 0 ? (
        <div className="border-b border-[#00000014] pb-base-24">
          <InstallationService
            productConfigOptions={productConfigOptions}
            productStatus={productStatus}
            handleUpdateService={handleUpdateService}
          />
        </div>
      ) : null}

      {/* 选择门店 */}
      {deliveryMethodPickup ? (
        <StoreSelector
          isDisabled={productStatus.isEverythingOutOfStock || productStatus.isOutOfStock || false}
          selectedStore={selectStore}
          handleViewStorePop={handleViewStorePop}
        />
      ) : null}

      {/* 绑定车架号 */}
      {isBoundCart && (
        <div className="font-miSansSemibold520 text-[18px] leading-none">绑定车架号</div>
      )}
    </div>
  )
}

export default ProductInfo
