'use client'

import { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { generateOSSUrl, OrderDetailItem, OrderReturnDetailItem } from '@ninebot/core'

import { CustomImage, IconArrow } from '@/components'
import { useMediaQuery } from '@/hooks'

type ProductItemProps = {
  productInfo?: OrderDetailItem | OrderReturnDetailItem
  showAttr?: boolean
  isReturn?: boolean
  setInstructionVisible?: () => void
  isShowInstruction?: boolean
  small?: boolean
}

/**
 * 产品 Item
 */
const ProductItem = (props: ProductItemProps) => {
  const {
    productInfo,
    showAttr = true,
    isReturn = false,
    setInstructionVisible,
    isShowInstruction = false,
    small = false,
  } = props

  const responsive = useMediaQuery()

  const configOptions = useMemo(() => productInfo?.config_selected_options || [], [productInfo])
  const product = productInfo?.product

  const getI18nString = useTranslations('Common')

  /**
   * 是否自提产品
   */
  const isPickupProduct = useMemo(() => {
    if (productInfo && 'store_info' in productInfo) {
      return !!productInfo?.store_info?.store_name
    }

    return false
  }, [productInfo])

  /**
   * 是否虚拟产品
   */
  const isVirtualProduct = useMemo(() => {
    return productInfo?.product_type === 'virtual' || productInfo?.product_type === 'bundle'
  }, [productInfo?.product_type])

  /**
   * 是否数字会员产品
   */
  const isMembershipProduct = useMemo(() => {
    if (productInfo && 'third_platform_member_code' in productInfo) {
      return Number(productInfo?.third_platform_member_code?.items?.length) > 0
    }
    return false
  }, [productInfo])

  /**
   * 是否是 Configurable 商品
   */
  const isConfigurableProduct = useMemo(() => {
    return configOptions.length > 0
  }, [configOptions])

  /**
   * configurable 商品的 option value
   */
  const configurableProductOptionValue = useMemo(() => {
    return isConfigurableProduct
      ? configOptions.map((option) => {
          return option?.value_label
        })
      : []
  }, [configOptions, isConfigurableProduct])

  if (!productInfo) return null

  return (
    <div className="flex items-center justify-between">
      <div className={`flex flex-1 justify-between gap-base-16 ${small ? '' : 'items-center'}`}>
        <div className="relative">
          <CustomImage
            width={small ? 60 : responsive?.['2xl'] ? 98 : 68}
            height={small ? 60 : responsive?.['2xl'] ? 98 : 68}
            displayMode="fixed"
            borderRadius={8}
            objectFit="cover"
            src={product?.image?.url || ''}
            alt={product?.name || ''}
          />
          {'requisition_status_label' in productInfo && productInfo?.requisition_status_label ? (
            <div className="absolute bottom-0 flex h-[22px] w-full items-center justify-center rounded-b-base bg-[#FEE5E5]">
              <span className="font-miSansRegular330 text-[12px] leading-none text-primary">
                {productInfo?.requisition_status_label}
              </span>
            </div>
          ) : null}
        </div>

        <div className="flex flex-1 flex-col gap-[4px]">
          <div className="flex items-start gap-[4px] 2xl:items-center">
            {isPickupProduct && (
              <CustomImage
                containerClassName="top-[4px] 2xl:top-0 flex-none"
                width={16}
                height={16}
                displayMode="fixed"
                src={generateOSSUrl('/icons/store.png')}
                alt=""
              />
            )}
            <div className="line-clamp-2 text-[16px] leading-[1.4] text-[#0F0F0F]">
              {product?.name}
            </div>
          </div>

          {/* 虚拟商品说明按钮 */}
          {isShowInstruction && isVirtualProduct && !isReturn && !isMembershipProduct && (
            <button className="h-auto w-auto py-2" onClick={setInstructionVisible}>
              <div className="flex items-center">
                <span className="text-sm text-gray-3">{getI18nString('instructions')}</span>
                <IconArrow color="#00000050" rotate={-90} />
              </div>
            </button>
          )}

          {/* 商品属性 */}
          {showAttr &&
            !isVirtualProduct &&
            (isConfigurableProduct || isPickupProduct ? (
              <div className="inline-flex">
                {isConfigurableProduct && (
                  <div className="w-auto font-miSansRegular330 text-[12px] leading-none text-[#00000099]">
                    {configurableProductOptionValue.join('，')}
                  </div>
                )}
              </div>
            ) : null)}
        </div>
      </div>
    </div>
  )
}

export default ProductItem
