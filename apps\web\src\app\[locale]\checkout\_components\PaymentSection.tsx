'use client'

import Image from 'next/image'
import { useTranslations } from 'next-intl'
import { TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import {
  checkoutPaymentMethodsSelector,
  checkoutSelectedPaymentMethodSelector,
  setCheckoutPlaceOrderPaymentMethodCode,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Radio } from 'antd'

import { CheckOutlined } from '@/components'
// 定义支付方式类型
type AvailablePaymentMethod = {
  __typename?: 'AvailablePaymentMethod'
  code: string
  title: string
  logo?: string | null
}

export default function PaymentSection() {
  const dispatch = useAppDispatch()
  const getI18nString = useTranslations('Common')
  const paymentMethods = useAppSelector(checkoutPaymentMethodsSelector)
  const selectedPaymentMethod = useAppSelector(checkoutSelectedPaymentMethodSelector)
  const { reportEvent } = useVolcAnalytics()

  /**
   * 设置选中的 payment method
   */
  const handleSelectPaymentMethod = (code: string) => {
    reportEvent(TRACK_EVENT.shop_payment_method_click, {
      payment_method_id: code,
    })
    dispatch(setCheckoutPlaceOrderPaymentMethodCode(code))
  }

  return (
    <div>
      <h2 className="mb-base-24 font-miSansDemiBold450 text-2xl">
        {getI18nString('recommended_payment_method')}
      </h2>
      <Radio.Group
        block
        optionType="button"
        className="custom-radio-group payment-radio-group"
        style={{ width: '100%' }}
        value={selectedPaymentMethod?.code}
        onChange={(e) => handleSelectPaymentMethod(e.target.value)}>
        {paymentMethods
          .filter((payment): payment is AvailablePaymentMethod => payment !== null)
          .map((payment) => (
            <Radio
              key={payment.code}
              value={payment.code}
              style={{ width: '100%', maxWidth: '276px' }}>
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-[8px] leading-none">
                  {payment.logo && (
                    <Image src={payment.logo} alt={payment.title} width={24} height={24} />
                  )}
                  <span className="font-miSansRegular330 text-[16px] leading-[140%] text-[#000000]">
                    {payment.title}
                  </span>
                </div>
                {selectedPaymentMethod?.code === payment.code && <CheckOutlined />}
              </div>
            </Radio>
          ))}
      </Radio.Group>
    </div>
  )
}
