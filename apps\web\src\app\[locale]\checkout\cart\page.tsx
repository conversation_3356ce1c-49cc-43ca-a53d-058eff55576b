'use client'
import { useEffect, useState } from 'react'
import { useAuth, TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'

import { IconArrow, RecommendProducts, Skeleton } from '@/components'

import { CartProvider, useCart } from './context/cartContext'
import { CartEmpty, CartItemList, CartSummary, CartTabs, ProductOptionsPopup } from './components'

/**
 * 购物车页面
 */
const CartContent = () => {
  const { showContent, cartAllProductsTotal, cartFilteredProducts } = useCart()
  const { isLoggedIn, redirectLoginUrl } = useAuth()
  const { reportEvent } = useVolcAnalytics()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  /**
   * 埋点：购物车页曝光
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_cart_page_exposure)
  }, [reportEvent])

  return (
    <div className="max-container mx-auto !pt-base-48">
      {/* 标题 */}
      <div className="mb-16 flex items-center">
        <h2 className="mr-base-12 font-miSansDemiBold450 text-h2">购物车</h2>
        {cartAllProductsTotal ? (
          <span className="text-3xl text-gray-500">({cartAllProductsTotal})</span>
        ) : null}
      </div>

      {/* 登录提示条 */}
      {mounted && !isLoggedIn && (
        <div className="mb-base-24 flex items-baseline gap-8 rounded-[4px] bg-[#FEF2F2] px-base-16 py-4 text-[12px] leading-none text-primary">
          <span>登录后享受更多优惠</span>
          <button
            onClick={() => {
              redirectLoginUrl()
            }}
            className="flex items-center gap-[2px]">
            去登录
            <IconArrow rotate={-90} color="#DA291C" size={12} />
          </button>
        </div>
      )}

      {showContent ? (
        <>
          {cartAllProductsTotal ? (
            <>
              {/* 标签页 */}
              <CartTabs />

              {/* 商品列表和结算区域 */}
              {cartFilteredProducts.length > 0 ? (
                <>
                  <div className="flex">
                    <CartItemList items={cartFilteredProducts} />
                    <div className="mx-[40px] w-[1px] border-r border-[#00000015] 3xl:mx-[88px]" />
                    <CartSummary />
                  </div>
                </>
              ) : (
                <CartEmpty />
              )}
            </>
          ) : (
            <CartEmpty />
          )}

          {/* 为你推荐 */}
          <RecommendProducts />

          <ProductOptionsPopup />
        </>
      ) : (
        <div className="flex">
          <div className="flex-1">
            <Skeleton style={{ height: 800, width: '100%' }} />
          </div>

          <div className="mx-[40px] w-[1px] border-r border-[#00000015] 3xl:mx-[88px]" />

          <div className="w-full max-w-[368px] 2xl:max-w-[380px] 3xl:max-w-[412px]">
            <Skeleton style={{ height: 400, width: '100%' }} />
          </div>
        </div>
      )}
    </div>
  )
}

const CartPage = () => {
  return (
    <CartProvider>
      <CartContent />
    </CartProvider>
  )
}

export default CartPage
