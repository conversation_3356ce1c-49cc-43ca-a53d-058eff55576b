import React from 'react'
import { useTranslations } from 'next-intl'
import { generateOSSUrl, Membership, OrderDetailItem } from '@ninebot/core'

import { CustomImage, Modal } from '@/components'

import ProductItem from '../ProductItem'

type MembershipPopupProps = {
  popupVisible: boolean
  copyToClipboard: (text: string) => void
  closePopup: () => void
  data: Membership[]
  productInfo?: OrderDetailItem
}

/**
 * 数字会员弹窗
 */
const MembershipPopup = ({
  popupVisible,
  copyToClipboard,
  closePopup,
  data,
  productInfo,
}: MembershipPopupProps) => {
  const getI18nString = useTranslations('Common')

  /**
   * 券码状态
   */
  const renderStatus = (item: Membership) => {
    if (!item) return null

    const { status, code, status_label } = item

    if (status === '0') {
      return (
        <button
          className="mr-8 flex h-[32px] items-center justify-center rounded-full border border-primary px-base-16"
          onClick={() => copyToClipboard(code || '')}>
          <div className="font-miSansRegular330 text-[14px] leading-[20px] text-primary">
            {getI18nString('coupon_code_copy')}
          </div>
        </button>
      )
    } else {
      return (
        <div>
          <CustomImage
            width={80}
            height={80}
            style={{
              top: 10,
              right: 0,
            }}
            src={generateOSSUrl('/icons/coupon_code_info_status.png')}
            alt=""
          />
          <div className="right-[-23px] top-[26px] -rotate-[40deg] transform font-miSansRegular330 text-[14px] leading-[19px] text-[#BBBBBD]">
            {status_label}
          </div>
        </div>
      )
    }
  }

  return (
    <Modal
      isOpen={popupVisible}
      onClose={closePopup}
      title={getI18nString('coupon_code')}
      width={600}
      okButtonProps={{
        style: {
          display: 'none',
        },
      }}
      cancelButtonProps={{
        style: {
          display: 'none',
        },
      }}>
      <div className="mt-[12px] max-h-[400px] flex-1 space-y-base-12">
        {/* 产品信息展示 */}
        {productInfo && <ProductItem small productInfo={productInfo} showAttr={false} />}

        {data.map((item, index) => (
          <div
            key={index}
            className="flex h-[100px] items-center justify-between overflow-hidden rounded-[12px] bg-[#F3F3F4]">
            <div className="relative flex h-full items-center justify-center px-base-24">
              <div className="absolute -right-[8px] -top-[8px] h-[16px] w-[16px] rounded-full bg-white" />
              <div className="flex flex-1 flex-row items-center justify-center">
                <div
                  className={`font-miSansDemiBold450 text-[16px] leading-[22px] text-[#000000] ${item?.status === 'used' ? 'text-[#86868B]' : ''}`}>
                  {getI18nString('coupon_index', { key: index + 1 })}
                </div>
              </div>
              <div className="absolute -bottom-[8px] -right-[8px] h-[16px] w-[16px] rounded-full bg-white" />
            </div>
            <div className="h-[62px] w-[1px] border-l border-dashed border-[#E1E1E4] bg-transparent" />
            <div className="flex-1">
              <div className="flex flex-1 justify-between">
                <div className="ml-16 flex flex-1 flex-row items-center justify-between">
                  <div>
                    <div
                      className={`mb-[5px] font-miSansDemiBold450 text-[16px] leading-[21px] text-[#000000] ${item?.status !== '0' ? 'text-[#86868B]' : ''}`}>
                      {item?.code}
                    </div>
                    <div className="font-miSansDemiBold450 text-[12px] leading-[14px] text-[#86868B]">
                      {getI18nString('coupon_code_expires', {
                        key: item?.expired_at?.split(' ')[0],
                      })}
                    </div>
                  </div>
                  {renderStatus(item)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Modal>
  )
}

export default MembershipPopup
