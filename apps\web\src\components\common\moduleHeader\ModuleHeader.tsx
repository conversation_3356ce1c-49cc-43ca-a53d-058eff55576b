import {
  ButtonUrl,
  encodeBase64,
  generateEvent<PERSON>arams,
  isBase64,
  TRACK_EVENT,
  URL_TYPE,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import clsx from 'clsx'

import { IconArrow } from '@/components'

/**
 * @description 模块头部
 * @param {string} title - 模块标题
 * @param {string} buttonText - 模块按钮文字
 * @param {boolean} showHorizonModule - 是否显示水平模块
 * @param {function} openPage - 打开页面
 */

type ModuleHeaderProps = {
  title: string
  buttonText: string
  url: ButtonUrl
  timeCounter?: JSX.Element | null
}

const ModuleHeader = ({ title, buttonText, url, timeCounter }: ModuleHeaderProps) => {
  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()
  return (
    <div className="mb-base-32 flex items-center justify-between">
      <div className="flex items-center gap-base-32">
        <div
          className={clsx(
            'font-miSansMedium450 text-h3',
            timeCounter ? 'text-primary' : 'text-black',
          )}>
          {title}
        </div>
        {timeCounter}
      </div>

      {(!!url.url || !!url.value) && (
        <button
          className="hover-line flex items-center gap-[6px]"
          onClick={() => {
            if (url.type === URL_TYPE.category) {
              reportEvent(
                TRACK_EVENT.shop_homepage_category_view_all_button_click,
                generateEventParams(url as ButtonUrl),
              )

              openPage({
                ...url,
                type: url.type,
                value: isBase64(String(url.value)) ? url.value : encodeBase64(String(url.value)),
              })
              return
            }

            // 添加折扣查看全部按钮点击埋点
            reportEvent(
              TRACK_EVENT.shop_homepage_discount_view_all_button_click,
              generateEventParams(url as ButtonUrl),
            )

            openPage({ ...url })
          }}>
          <div className="font-miSansMedium450 text-lg">{buttonText}</div>
          <IconArrow size={20} rotate={-90} color="currentColor" />
        </button>
      )}
    </div>
  )
}

export default ModuleHeader
