'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  cn,
  generateOSSUrl,
  IconPlus,
  Membership,
  mergeStyles,
  NCoinView,
  OrderDisabledReturnProducts,
  OrderReturnProducts,
  OrderShipmentTracking,
  OrderTracking,
  OrderVouchers,
  PaymentMethod,
  Price,
  resolveCatchMessage,
  ROUTE,
  setReturnAddressId,
  sleep,
  useClipboard,
  useDebounceFn,
  useGetOrderDetailQuery,
  useLazyGetOrderLogisticsQuery,
  useLazyGetOrderRequisitionListQuery,
  useLoadingContext,
  useOrderDetail,
  useToastContext,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Image } from 'antd-mobile'

import { JumpToCustomerService, StorePopup } from '@/businessComponents'
import {
  Arrow,
  CustomButton,
  CustomNavBar,
  IconPosition,
  IconService,
  PayMethodPopup,
  PickupStoreCard,
} from '@/components'
import { commonStyles } from '@/constants'
import { useRouter } from '@/i18n/navigation'

import {
  CouponPopup,
  InstructionPopup,
  LogisticsPopup,
  MembershipPopup,
  OrderDetailFooter,
  OrderDetailProductList,
  OrderDetailSkeleton,
  ReturnPopup,
} from './components'

const styles = {
  ...commonStyles,
  container: 'mb-[8px] py-base-16 px-[12px] bg-white rounded-[12px] ',
  couponStatus: 'py-[4px] px-[12px] bg-[#FEE5E5] rounded-[4px] ',
  couponStatusText: 'text-[14px] leading-8 text-[#DA291C] font-miSansDemiBold450 ',
  detailItem: 'flex items-center justify-between',
  signStyle: 'text-[#DA291C] mr-[2px]',
}

type AccountOrderDetailProps = {
  orderNumber?: string
}

const AccountOrderDetail = ({ orderNumber }: AccountOrderDetailProps) => {
  const searchParams = useSearchParams()
  const getI18nString = useTranslations('Common')
  const router = useRouter()
  const loading = useLoadingContext()
  const toast = useToastContext()
  const { openPage } = useNavigate()
  const { copyToClipboard } = useClipboard()

  const dispatch = useAppDispatch()

  const [paymentExpand, setPaymentExpand] = useState(false)

  const [storeVisible, setStoreVisible] = useState(false)

  const [paymentVisible, setPaymentVisible] = useState(false)
  const [paymentsList, setPaymentsList] = useState<PaymentMethod[]>([])

  const [pickupVisible, setPickupVisible] = useState(false)

  const [couponList, setCouponList] = useState<OrderVouchers>([])
  const [couponVisible, setCouponVisible] = useState(false)

  const [logisticsData, setLogisticsData] = useState<OrderTracking>()
  const [logisticsVisible, setLogisticsVisible] = useState(false)

  const [instructionData, setInstructionData] = useState('')
  const [instructionVisible, setInstructionVisible] = useState(false)

  const [returnVisible, setReturnVisible] = useState(false)
  const [returnProducts, setReturnProducts] = useState<OrderReturnProducts>([])
  const [disabledProducts, setDisabledProducts] = useState<OrderDisabledReturnProducts>([])

  const [membershipList, setMembershipList] = useState<Membership[]>([])
  const [membershipVisible, setMembershipVisible] = useState(false)

  const [getOrderLogistics] = useLazyGetOrderLogisticsQuery()
  const [getOrderRequisition] = useLazyGetOrderRequisitionListQuery()

  const {
    data: response,
    isLoading,
    isFetching,
    refetch,
  } = useGetOrderDetailQuery(
    {
      filter: {
        nid: { eq: orderNumber || '' },
      },
    },
    {
      refetchOnMountOrArgChange: true,
    },
  )
  const orderData = response?.customer?.orders?.items?.[0]

  const currentTime = useRef(new Date().getTime())

  const {
    isMigrated,
    migrationOrder,
    migrationProductCount,
    migrationTotalNCoin,
    showShippingAddress,
    isTracking,
    hasNCoin,
    isOnlyNCoin,
    isFree,
    isAllVirtualProduct,
    isReturnInBatches,
    isUnPaid,
    isSkinSound,
    isShowAddressByShippingMethod,
  } = useOrderDetail(orderData)

  /**
   * 是否同时存在N币和现金折扣
   */
  const hasBothDiscounts = useMemo(
    () =>
      Number(orderData?.total?.discounts?.[0]?.distribute_amount) > 0 &&
      Number(orderData?.total?.discounts?.[0]?.distribute_ncoin) > 0,
    [orderData?.total?.discounts],
  )

  /**
   * 查询物流信息
   */
  const openLogisticsPopup = (track: OrderShipmentTracking) => {
    if (track?.carrier_code && track?.track_number && orderData?.encrypt?.nid) {
      loading.show()
      getOrderLogistics({
        company: track.carrier_code,
        trackNo: track.track_number,
        orderNumber: orderData.encrypt.nid,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (Number(res?.getOrderTracking?.length) > 0) {
            setLogisticsData(res?.getOrderTracking?.[0])
            setLogisticsVisible(true)
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  }

  /**
   * 申请退款退货
   */
  const { run: handleReturnOrder } = useDebounceFn(() => {
    if (isReturnInBatches) {
      if (orderData?.encrypt?.nid) {
        loading.show()
        getOrderRequisition({
          orderNumber: orderData.encrypt.nid,
        })
          .unwrap()
          .then(async (res) => {
            loading.hide()
            await sleep(500)
            if (Number(res?.orderRequisitionList?.items?.length) > 0) {
              setReturnProducts(res?.orderRequisitionList?.items)
            }
            if (Number(res?.orderRequisitionList?.requested_items?.length) > 0) {
              setDisabledProducts(res?.orderRequisitionList?.requested_items)
            }
            setReturnVisible(true)
          })
          .catch(async (error) => {
            loading.hide()
            await sleep(500)
            toast.show({
              icon: 'fail',
              content: resolveCatchMessage(error) as string,
            })
          })
      } else {
        toast.show({
          icon: 'fail',
          content: getI18nString('fetch_data_error'),
        })
      }
    } else {
      dispatch(setReturnAddressId(orderData?.shipping_address?.address_id || ''))
      openPage({
        route: ROUTE.accountOrderReturn,
        queryParams: {
          orderNumber: orderData?.encrypt?.nid || '',
          isShowAddress: isShowAddressByShippingMethod,
        },
      })
    }
  })

  /**
   *  查看使用说明
   */
  const handleOpenInstruction = (html: string) => {
    setInstructionData(html)
    setInstructionVisible(true)
  }

  useEffect(() => {
    if (!isLoading && isFetching) {
      loading.show()
    } else {
      loading.hide()
    }
  }, [loading, isLoading, isFetching])

  /**
   * 自定义返回按钮操作事件
   */
  const handleNavLeftPress = useCallback(() => {
    const from = searchParams.get('from') || ''
    // 从以下页面到当前页面的流程，返回到首页
    if ([ROUTE.checkoutPending, ROUTE.checkoutResult, ROUTE.checkoutCart].includes(from)) {
      router.replace('/')
    }

    // 默认返回行为
    router.back()
  }, [router, searchParams])

  useEffect(() => {
    // 如果没拿到订单数据，跳到订单列表
    if (!isLoading && !orderData) {
      openPage({
        route: ROUTE.accountOrder,
      })
    }
  }, [isLoading, orderData, openPage])

  return (
    <div className="flex flex-col">
      <CustomNavBar
        title={!isMigrated ? orderData?.status_tab_label || '' : migrationOrder?.status || ''}
        right={
          <div className="float-end">
            <JumpToCustomerService
              udeskParams={{
                type: 'order',
                targetId: '',
              }}>
              <IconService />
            </JumpToCustomerService>
          </div>
        }
        onBack={handleNavLeftPress}
      />
      {isLoading || !orderData ? (
        <div className="min-h-[calc(100vh-116px)] bg-gray-base">
          <OrderDetailSkeleton />
        </div>
      ) : (
        <div className="min-h-[calc(100vh-116px)] bg-gray-base px-[12px] pb-8 pt-[10px]">
          {/* 退款失败原因 */}
          {orderData?.requisition ? (
            <div className="mb-[8px] flex flex-row rounded-[8px] bg-[#FF7B211A] px-[16px] py-[12px]">
              <Image
                style={{
                  width: 18,
                  height: 18,
                  marginRight: 12,
                }}
                src={generateOSSUrl('/icons/close-red.png')}
                fit="cover"
                alt="close"
              />
              <div>
                <div className={styles.font_14_bold}>{getI18nString('review_rejected')}</div>
                {orderData?.requisition?.reason ? (
                  <div className="mt-[4px] font-miSansRegular330 text-[12px] leading-[18px] text-[#86868B]">
                    {orderData?.requisition?.reason}
                  </div>
                ) : null}
              </div>
            </div>
          ) : null}
          {/* 地址 */}
          {(isMigrated || showShippingAddress) && (
            <div className={`flex flex-grow items-center ${styles.container}`}>
              <div className="ml-[4px] mr-[8px]">
                <IconPosition />
              </div>
              <div className="flex-1">
                <div className={styles.flex_start}>
                  <span className={styles.font_16_bold}>
                    {!isMigrated ? orderData?.shipping_address?.firstname : migrationOrder?.name}
                  </span>
                  <span className={styles.font_16_bold + 'mx-4'}>
                    {!isMigrated ? orderData?.shipping_address?.telephone : migrationOrder?.tel}
                  </span>
                  {/* <span className='py-[1px] px-[4px] bg-[#FEE5E5] rounded-[4px] text-[12px] leading-[16px] text-[#FF3B3B] font-miSansRegular330'>公司</span> */}
                </div>
                {isMigrated ? (
                  <div className="mt-[8px] font-miSansRegular330 text-[14px] leading-[22px] text-[#86868B]">
                    {migrationOrder?.address}
                  </div>
                ) : (
                  <div className="mt-[8px] font-miSansRegular330 text-[14px] leading-[22px] text-[#86868B]">
                    {orderData?.shipping_address?.region} {orderData?.shipping_address?.city}{' '}
                    {orderData?.shipping_address?.district}{' '}
                    {orderData?.shipping_address?.street.join()}
                  </div>
                )}
              </div>
            </div>
          )}
          {/* 自提 */}
          {Number(orderData?.store_info?.length) > 0 && orderData?.store_info?.[0] && (
            <div className={styles.container}>
              <PickupStoreCard
                storeInfo={orderData.store_info[0]}
                onNavPress={() => setStoreVisible(true)}
              />
            </div>
          )}
          {/* 订单详情 */}
          <div className={styles.container}>
            <div className={mergeStyles([styles.font_16_bold, 'mb-[24px]'])}>
              {getI18nString('order_detail')}
            </div>
            <div className="flex flex-col gap-8">
              <div className={styles.flex_row}>
                <div className={styles.font_14_light}>{getI18nString('order_number')}</div>
                <div className={styles.flex_end}>
                  <div className={mergeStyles([styles.font_14_light, styles.font_gray])}>
                    {!isMigrated ? orderData?.number : migrationOrder?.order_num}｜
                  </div>
                  <div
                    onClick={() => {
                      copyToClipboard(
                        !isMigrated ? orderData?.number : migrationOrder?.order_num || '',
                      )
                    }}>
                    <div className={mergeStyles([styles.font_14_light])}>
                      {getI18nString('copy')}
                    </div>
                  </div>
                </div>
              </div>
              <div className={styles.flex_row}>
                <span className={styles.font_14_light}>{getI18nString('ordered_time')}</span>
                <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                  {isMigrated ? migrationOrder?.create_date : orderData?.order_date}
                </span>
              </div>
              {!!orderData?.pickup_phone && (
                <div className={styles.flex_row}>
                  <span className={styles.font_14_light}>{getI18nString('pickup_phone')}</span>
                  <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                    {orderData?.pickup_phone}
                  </span>
                </div>
              )}
              {!!orderData?.remark_comment && (
                <div className={cn(styles.flex_row, 'gap-[8px]')}>
                  <span className={cn(styles.font_14_light, 'flex-none')}>
                    {getI18nString('remark')}
                  </span>
                  <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                    {orderData?.remark_comment}
                  </span>
                </div>
              )}
              {orderData?.can_cancel_requisition !== true ? (
                <>
                  {!!orderData?.pickup_info?.status_label && (
                    <div className="flex items-center justify-between">
                      <span className={styles.font_14_light}>{getI18nString('pickup_code')}</span>
                      <div
                        className={styles.flex_row}
                        onClick={() => {
                          setPickupVisible(true)
                        }}>
                        <div
                          className={
                            orderData?.pickup_info?.status === '0' ||
                            orderData?.pickup_info?.status === '2'
                              ? styles.couponStatus
                              : mergeStyles([styles.couponStatus, 'bg-[#F3F3F4]'])
                          }>
                          <span
                            className={cn(
                              orderData?.pickup_info?.status === '0' ||
                                orderData?.pickup_info?.status === '2'
                                ? styles.couponStatusText
                                : cn([styles.couponStatusText, 'text-[#0C0C0D]']),
                              'font-miSansRegular330',
                            )}>
                            {orderData?.pickup_info?.status_label}
                          </span>
                        </div>
                        <Arrow color="#86868B" />
                      </div>
                    </div>
                  )}
                  {!!orderData?.pickup_info?.status_label &&
                    !!orderData?.pickup_info?.expired_info && (
                      <div
                        className={mergeStyles([
                          'mb-8 text-right font-miSansDemiBold450 text-[12px] leading-[1.2]',
                          styles.font_gray,
                        ])}>
                        {orderData?.pickup_info?.expired_info}
                      </div>
                    )}
                </>
              ) : null}
            </div>
          </div>
          {/* 车辆信息 */}
          {Number(orderData?.car_vin?.length) > 0 && (
            <div className={styles.container}>
              <div className={mergeStyles([styles.font_16_bold, 'mb-[24px]'])}>
                {getI18nString('car_info')}
              </div>
              {orderData?.car_vin?.map((car, index) => (
                <div key={index}>
                  <Image
                    style={{
                      width: 88,
                      height: 88,
                      borderRadius: 8,
                      backgroundColor: '#F8F8F9',
                      marginBottom: 12,
                    }}
                    src={car?.device_image}
                    fit="cover"
                    alt={car?.device_name}
                  />
                  <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                    <span className={styles.font_14_light}>{getI18nString('car_modal')}</span>
                    <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                      {car?.device_name}
                    </span>
                  </div>
                  <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                    <span className={styles.font_14_light}>{getI18nString('sn')}</span>
                    <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                      {car?.snNo}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
          {/* 包裹 */}
          {isTracking &&
            orderData?.shipment_tracking?.map((track, index) => (
              <div key={track?.track_number} className={styles.container}>
                <span className={mergeStyles([styles.font_16_bold + 'mb-[4px]'])}>
                  {getI18nString('package')}
                  {index + 1}
                </span>

                {Number(track?.products?.length) > 0
                  ? track?.products?.map((product) => (
                      <div key={product?.product_sku} className="my-8 flex flex-row">
                        <Image
                          style={{
                            width: 80,
                            height: 80,
                          }}
                          src={product?.product?.image?.url || ''}
                          fit="cover"
                          alt={product?.product?.name || ''}
                        />
                        <span className={styles.font_16 + 'ml-[12px] flex-1'}>
                          {product?.product?.name}
                        </span>
                      </div>
                    ))
                  : null}

                <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                  <span className={styles.font_14_light}>{getI18nString('logistics')}</span>
                  <span className={styles.font_14_light}>{track?.carrier_title}</span>
                </div>
                <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                  <span className={styles.font_14_light}>{getI18nString('track_number')}</span>
                  <div className={styles.flex_row}>
                    <span className={mergeStyles([styles.font_14_light + styles.font_gray])}>
                      {track?.track_number}｜
                    </span>
                    <div
                      onClick={() => {
                        copyToClipboard(track?.track_number || '')
                      }}>
                      <span className={mergeStyles([styles.font_14_light])}>
                        {getI18nString('copy')}
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  className={styles.flex_row}
                  onClick={() => {
                    openLogisticsPopup(track)
                  }}>
                  <div className={styles.font_14_light}>{getI18nString('track_info')}</div>
                  <div className={styles.flex_row}>
                    <span className={mergeStyles([styles.font_14_light])}>{track?.state}</span>
                    <Arrow />
                  </div>
                </div>
              </div>
            ))}
          {/* 商品清单 */}
          <OrderDetailProductList
            isSkinSound={isSkinSound || false}
            isMigrated={isMigrated}
            migrationData={migrationOrder}
            orderData={orderData}
            handleOpenInstruction={handleOpenInstruction}
            setCouponList={setCouponList}
            setCouponVisible={setCouponVisible}
            setMembershipList={setMembershipList}
            setMembershipVisible={setMembershipVisible}
          />
          {/* 付款信息 */}
          <div className={styles.container}>
            <div
              className="flex items-center justify-between"
              onClick={() => {
                setPaymentExpand((pre) => !pre)
              }}>
              <div className={styles.font_16_bold}>
                {isUnPaid ? getI18nString('should_pay') : getI18nString('has_paid')}
              </div>
              <div className={styles.flex_end}>
                <span className="mr-[4px] font-miSansRegular330 text-[12px] leading-[14px] text-[#6E6E73]">
                  {getI18nString('total_count', {
                    key: isMigrated ? migrationProductCount : orderData?.quantity_ordered,
                  })}
                </span>
                {isOnlyNCoin ? (
                  <NCoinView
                    number={isMigrated ? migrationTotalNCoin : orderData?.ncoin_pay?.grand_total}
                    iconStyle={{ size: 14 }}
                    textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                    showZero
                  />
                ) : hasNCoin ? (
                  <>
                    <Price
                      price={orderData?.total?.grand_total}
                      currencyStyle={mergeStyles([styles.font_14, styles.font_red])}
                      textStyle={mergeStyles([styles.font_14, styles.font_red])}
                      fractionStyle={mergeStyles([styles.font_14, styles.font_red])}
                    />
                    {orderData?.ncoin_pay?.grand_total > 0 && (
                      <>
                        <div className="mx-[4px]">
                          <IconPlus />
                        </div>
                        <NCoinView
                          number={orderData?.ncoin_pay?.grand_total}
                          iconStyle={{ size: 14 }}
                          textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                          showZero
                        />
                      </>
                    )}
                  </>
                ) : (
                  <Price
                    price={orderData?.total?.grand_total}
                    currencyStyle={mergeStyles([styles.font_14, styles.font_red])}
                    textStyle={mergeStyles([styles.font_14, styles.font_red])}
                    fractionStyle={mergeStyles([styles.font_14, styles.font_red])}
                  />
                )}

                <div className="ml-[4px]">
                  <Arrow rotate={!paymentExpand ? 90 : -90} />
                </div>
              </div>
            </div>
            {paymentExpand ? (
              <div className="mt-base-24 space-y-8">
                <div className={styles.detailItem}>
                  <span className={styles.font_14_light}>{getI18nString('total_price')}</span>

                  {isOnlyNCoin ? (
                    <NCoinView
                      number={isMigrated ? migrationTotalNCoin : orderData?.ncoin_pay?.subtotal}
                      iconStyle={{ size: 14 }}
                      textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                      showZero
                    />
                  ) : hasNCoin ? (
                    <div className={styles.flex_row}>
                      <Price
                        price={orderData?.total?.subtotal}
                        currencyStyle={styles.font_14}
                        textStyle={styles.font_14}
                        fractionStyle={styles.font_14}
                      />
                      <div className="mx-[4px]">
                        <IconPlus />
                      </div>
                      <NCoinView
                        number={orderData?.ncoin_pay?.subtotal}
                        iconStyle={{ size: 14 }}
                        textStyle={mergeStyles([styles.font_14, 'leading-[18px]'])}
                        showZero
                      />
                    </div>
                  ) : (
                    <Price
                      price={orderData?.total?.subtotal}
                      currencyStyle={styles.font_14}
                      textStyle={styles.font_14}
                      fractionStyle={styles.font_14}
                    />
                  )}
                </div>
                {hasBothDiscounts ? (
                  <>
                    <div className={styles.detailItem}>
                      <span className={mergeStyles([styles.font_14_light, 'max-w-[70%]'])}>
                        {getI18nString('discount_price')}
                        {orderData?.total?.discounts?.[0]?.label
                          ? `(${orderData?.total?.discounts?.[0]?.label})`
                          : ''}
                      </span>
                      <div className={styles.flex_row}>
                        <span className={styles.signStyle}>-</span>
                        <Price
                          price={{
                            ...orderData?.total?.discounts?.[0]?.amount,
                            value: orderData?.total?.discounts?.[0]?.distribute_amount,
                          }}
                          currencyStyle={mergeStyles([styles.font_14, styles.font_red])}
                          textStyle={mergeStyles([styles.font_14, styles.font_red])}
                          fractionStyle={mergeStyles([styles.font_14, styles.font_red])}
                        />
                      </div>
                    </div>
                    <div className={styles.detailItem}>
                      <div className={styles.flex_row}>
                        <span className={styles.signStyle}>-</span>
                        <NCoinView
                          iconStyle={{ size: 14, background: '#DA291C' }}
                          textStyle={mergeStyles([styles.font_14, styles.font_red])}
                          number={orderData?.total?.discounts?.[0]?.distribute_ncoin ?? 0}
                        />
                      </div>
                    </div>
                  </>
                ) : (
                  <div className={styles.detailItem}>
                    <span className={mergeStyles([styles.font_14_light, 'max-w-[70%]'])}>
                      {getI18nString('discount_price')}
                      <span
                        dangerouslySetInnerHTML={{
                          __html: orderData?.total?.discounts?.[0]?.label
                            ? `(${orderData?.total?.discounts?.[0]?.label})`
                            : '',
                        }}></span>
                    </span>
                    <div className={styles.flex_end}>
                      <span className={styles.signStyle}>-</span>
                      {Number(orderData?.total?.discounts?.[0]?.distribute_ncoin) > 0 ? (
                        <NCoinView
                          iconStyle={{ size: 14, background: '#DA291C' }}
                          textStyle={mergeStyles([styles.font_14, styles.font_red])}
                          number={orderData?.total?.discounts?.[0]?.distribute_ncoin ?? 0}
                        />
                      ) : (
                        <Price
                          price={orderData?.total?.discounts?.[0]?.amount}
                          currencyStyle={mergeStyles([styles.font_14, styles.font_red])}
                          textStyle={mergeStyles([styles.font_14, styles.font_red])}
                          fractionStyle={mergeStyles([styles.font_14, styles.font_red])}
                        />
                      )}
                    </div>
                  </div>
                )}

                {!isOnlyNCoin && orderData?.ncoin_amount ? (
                  <div className={styles.detailItem}>
                    <span className={styles.font_14_light}>{getI18nString('n-coin_title')}</span>
                    <div className={styles.flex_row}>
                      <span className={styles.signStyle}>-</span>
                      <Price
                        price={{ value: orderData?.ncoin_amount }}
                        currencyStyle={mergeStyles([styles.font_14, styles.font_red])}
                        textStyle={mergeStyles([styles.font_14, styles.font_red])}
                        fractionStyle={mergeStyles([styles.font_14, styles.font_red])}
                      />
                    </div>
                  </div>
                ) : null}
                {!isAllVirtualProduct ? (
                  <div className={mergeStyles([styles.detailItem, 'mb-0'])}>
                    <span className={styles.font_14_light}>{getI18nString('shipping_price')}</span>
                    {orderData?.total?.total_shipping?.value ? (
                      <Price
                        price={orderData?.total?.total_shipping}
                        currencyStyle={styles.font_14}
                        textStyle={styles.font_14}
                        fractionStyle={styles.font_14}
                      />
                    ) : (
                      <span className={styles.font_14}>{getI18nString('shipping_free')}</span>
                    )}
                  </div>
                ) : null}
              </div>
            ) : null}
          </div>
          {/* 配送信息 */}
          {!isMigrated && (
            <div className={styles.container}>
              <div className={mergeStyles([styles.font_16_bold, 'mb-[24px]'])}>
                {getI18nString('shipping_info')}
              </div>
              <div className={styles.flex_row}>
                <span className={styles.font_14_light}>{getI18nString('shipping_method')}</span>
                <span className={styles.font_14}>{orderData?.delivery_method?.method_label}</span>
              </div>
            </div>
          )}

          {/* 支付 */}
          {!isMigrated && (
            <div className={styles.container}>
              <div className={mergeStyles([styles.font_16_bold, 'mb-[24px]'])}>
                {getI18nString('pay')}
              </div>
              {!isFree && (
                <div className={cn({ 'mb-[20px] space-y-[20px]': !!orderData?.paid_time })}>
                  <div className={mergeStyles([styles.flex_row])}>
                    <div className={styles.font_14_light}>{getI18nString('pay_method')}</div>
                    <div className={styles.flex_end}>
                      {orderData?.payment_methods?.[0]?.logo ? (
                        <Image
                          style={{
                            width: 24,
                            height: 24,
                            marginRight: 8,
                          }}
                          src={orderData.payment_methods[0].logo}
                          fit="cover"
                          alt={orderData?.payment_methods?.[0]?.name || ''}
                        />
                      ) : null}
                      <span className={styles.font_14}>
                        {orderData?.payment_methods?.[0]?.name}
                      </span>
                    </div>
                  </div>
                </div>
              )}
              {!!orderData?.paid_time && (
                <div className={mergeStyles([styles.flex_row])}>
                  <span className={styles.font_14_light}>{getI18nString('pay_time')}</span>
                  <span className={styles.font_14}>{orderData?.paid_time}</span>
                </div>
              )}
            </div>
          )}
          {/* 发票信息 */}
          {!!orderData?.invoice_info?.type && (
            <div className={styles.container}>
              <div className={mergeStyles([styles.font_16_bold, 'mb-[24px]'])}>
                {getI18nString('invoice_info')}
              </div>

              <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                <span className={styles.font_14_light}>{getI18nString('invoice_type')}</span>
                <span className={styles.font_14}> {orderData.invoice_info.type}</span>
              </div>
              <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                <span className={styles.font_14_light}>{getI18nString('invoice_header')}</span>
                <span className={styles.font_14}>{orderData.invoice_info?.title || ''}</span>
              </div>
              <div className={mergeStyles([styles.flex_row, 'mb-8'])}>
                <span className={styles.font_14_light}>{getI18nString('receive_type')}</span>
                <span className={styles.font_14}> {orderData.invoice_info?.email || ''}</span>
              </div>
              {!!orderData?.invoice_info?.zlink && (
                <div className={mergeStyles([styles.flex_row])}>
                  <span className={styles.font_14_light}>{getI18nString('invoice_link')}</span>
                  <CustomButton
                    customStyle={{
                      width: 'auto',
                      height: '3rem',
                      marginLeft: 4,
                      padding: '0 1.6rem',
                      borderRadius: 20,
                      borderWidth: 1,
                      borderColor: '#BBBBBD',
                    }}
                    fill="outline"
                    onClick={() => {
                      if (orderData?.invoice_info?.zlink) {
                        copyToClipboard(
                          orderData.invoice_info.zlink,
                          getI18nString('copy_invoice_tip'),
                        )
                        openPage({
                          route: ROUTE.webView,
                          webViewUrl: orderData.invoice_info.zlink,
                        })
                      }
                    }}>
                    <div className={commonStyles.font_14}>{getI18nString('copy_view')}</div>
                  </CustomButton>
                </div>
              )}
            </div>
          )}
          {/* 自提门店弹窗 */}
          {orderData?.store_info?.[0] && (
            <StorePopup
              store={orderData.store_info[0]}
              popupVisible={storeVisible}
              closePopup={() => {
                setStoreVisible(false)
              }}
            />
          )}
          {/* 查询物流弹窗 */}
          <LogisticsPopup
            popupVisible={logisticsVisible}
            closePopup={() => {
              setLogisticsVisible(false)
            }}
            data={logisticsData}
          />
          {/* 选择退货产品弹窗 */}
          <ReturnPopup
            products={returnProducts}
            orderNumber={orderData?.encrypt?.nid || ''}
            disabledProducts={disabledProducts}
            addressId={orderData?.shipping_address?.address_id || ''}
            popupVisible={returnVisible}
            closePopup={() => {
              setReturnProducts([])
              setReturnVisible(false)
            }}
          />
          {/* 使用说明弹窗 */}
          <InstructionPopup
            popupVisible={instructionVisible}
            closePopup={() => {
              setInstructionVisible(false)
            }}
            data={instructionData}
          />
          {/* 取货码弹窗 */}
          {orderData?.pickup_info && (
            <CouponPopup
              pickupInfo={orderData?.pickup_info}
              copyToClipboard={copyToClipboard}
              popupVisible={pickupVisible}
              closePopup={() => {
                setPickupVisible(false)
              }}
            />
          )}
          {/* 卷码弹窗 */}
          <CouponPopup
            couponList={couponList}
            copyToClipboard={copyToClipboard}
            popupVisible={couponVisible}
            closePopup={() => {
              setCouponVisible(false)
            }}
          />
          {/** 支付方式弹窗 */}
          <PayMethodPopup
            popupVisible={paymentVisible}
            tempOrderInfo={{
              orderId: orderData?.number,
              totalAmount: orderData?.total?.grand_total,
              defaultPayment: orderData?.payment_methods?.[0]?.type || '',
            }}
            paymentsList={paymentsList}
            closePopup={() => {
              setPaymentVisible(false)
            }}
          />
          {/* 数字会员弹窗 */}
          <MembershipPopup
            data={membershipList}
            copyToClipboard={copyToClipboard}
            popupVisible={membershipVisible}
            closePopup={() => {
              setMembershipVisible(false)
            }}
          />
        </div>
      )}

      {/* 底部操作 */}
      {orderData && (
        <OrderDetailFooter
          order={orderData}
          hasNCoin={hasNCoin}
          isOnlyNCoin={isOnlyNCoin}
          toast={toast}
          loading={loading}
          currentTime={currentTime?.current}
          setPaymentVisible={setPaymentVisible}
          setPaymentsList={setPaymentsList}
          refetchOrderDetail={refetch}
          handleReturnOrder={handleReturnOrder}
        />
      )}
    </div>
  )
}

export default AccountOrderDetail
