export default function House({ big = false }: { big?: boolean }) {
  return (
    <svg
      width={big ? '24' : '16'}
      height={big ? '24' : '17'}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.33472 9.16602V13.8327H12.6681V9.16602"
        stroke={big ? 'black' : '#444446'}
        strokeWidth="1.33333"
        strokeLinecap="square"
      />
      <path
        d="M13.2476 2.49902L13.395 2.96387L13.9175 4.60254L13.9224 4.61914L13.9263 4.63574L13.9448 4.70312L13.9468 4.71289L13.9487 4.72168C14.0118 4.99356 14.0155 5.27559 13.9585 5.54883C13.9015 5.82191 13.7859 6.07858 13.6216 6.30371C13.4573 6.52874 13.2481 6.71744 13.0083 6.85938C12.7687 7.00117 12.5027 7.09423 12.2271 7.13477C11.9512 7.17528 11.6689 7.16219 11.3979 7.09668C11.1271 7.03115 10.8711 6.91383 10.646 6.75C10.4208 6.58611 10.2305 6.37884 10.0894 6.13867C10.0368 6.04918 9.99414 5.95447 9.95654 5.8584C9.82883 6.18927 9.61442 6.48228 9.33252 6.70605C8.94061 7.01712 8.44703 7.17704 7.94678 7.16504C7.44657 7.15301 6.96211 6.96915 6.58643 6.63867C6.34415 6.42553 6.15861 6.15981 6.04346 5.86426C5.90971 6.2075 5.68152 6.50855 5.3833 6.73438C4.9718 7.04594 4.45691 7.19566 3.94189 7.16113C3.42666 7.12651 2.93629 6.90898 2.57275 6.54199C2.20806 6.1738 1.99774 5.681 1.99854 5.16113V5.14941L1.99951 5.13672L2.00244 5.04395L2.00342 5.02051L2.00635 4.99805L2.01611 4.90527L2.01807 4.89355L2.01904 4.88086C2.03263 4.78801 2.05358 4.6963 2.08057 4.60645L2.08252 4.59863L2.08545 4.5918L2.6167 2.95996L2.76611 2.49902H13.2476Z"
        stroke={big ? 'black' : '#444446'}
        strokeWidth="1.33333"
      />
    </svg>
  )
}
